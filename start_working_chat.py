#!/usr/bin/env python3
"""
Simple script to start the working chat application.
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_backend(port=8000):
    """Check if backend is running."""
    try:
        response = requests.get(f"http://127.0.0.1:{port}/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend():
    """Start the backend server."""
    backend_dir = Path(__file__).parent / "backend"
    
    print("🚀 Starting backend server...")
    
    # Start backend in a new console window
    if os.name == 'nt':  # Windows
        cmd = [
            "python", "-m", "uvicorn", "app.main:app", 
            "--host", "127.0.0.1", "--port", "8000", "--reload"
        ]
        process = subprocess.Popen(cmd, cwd=backend_dir, 
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
    else:  # Unix/Linux/Mac
        cmd = ["python3", "-m", "uvicorn", "app.main:app", 
               "--host", "127.0.0.1", "--port", "8000", "--reload"]
        process = subprocess.Popen(cmd, cwd=backend_dir)
    
    # Wait for backend to be ready
    print("⏳ Waiting for backend to start...")
    for i in range(20):
        if check_backend():
            print("✅ Backend server is ready!")
            return process
        time.sleep(1)
        if i % 5 == 0:
            print(f"   Still waiting... ({i+1}/20)")
    
    print("❌ Backend failed to start within 20 seconds")
    return None

def update_client_config():
    """Update client configuration."""
    env_file = Path(__file__).parent / "client" / ".env"
    
    print("⚙️ Updating client configuration...")
    
    try:
        with open(env_file, 'w') as f:
            f.write("# API Configuration\n")
            f.write("API_BASE_URL=http://127.0.0.1:8000\n")
        print("✅ Client configuration updated")
        return True
    except Exception as e:
        print(f"❌ Failed to update client config: {e}")
        return False

def start_client():
    """Start the client application."""
    client_dir = Path(__file__).parent / "client"
    
    print("🖥️ Starting client application...")
    
    cmd = ["python", "main.py"]
    
    try:
        subprocess.run(cmd, cwd=client_dir)
    except KeyboardInterrupt:
        print("\n👋 Client application closed by user")
    except Exception as e:
        print(f"❌ Client error: {e}")

def main():
    """Main function."""
    print("💬 Starting Working Chat Application")
    print("=" * 40)
    
    # Update client configuration
    if not update_client_config():
        return 1
    
    # Check if backend is already running
    if check_backend():
        print("✅ Backend is already running")
        backend_process = None
    else:
        # Start backend
        backend_process = start_backend()
        if not backend_process:
            print("\n❌ Failed to start backend server")
            return 1
    
    # Test backend
    print("\n🧪 Testing backend...")
    try:
        response = requests.get("http://127.0.0.1:8000/health")
        if response.status_code == 200:
            print("✅ Backend is working!")
        else:
            print(f"❌ Backend test failed: {response.status_code}")
            return 1
    except Exception as e:
        print(f"❌ Backend test error: {e}")
        return 1
    
    print("\n" + "=" * 40)
    print("🎉 Everything is ready!")
    print("\n💡 How to use:")
    print("1. Login with: testuser1750744580 / testpass123")
    print("2. Or create a new account")
    print("3. Click 'General Chat' to join the main room")
    print("4. Type messages and press Enter to send")
    print("=" * 40)
    
    try:
        # Start client
        start_client()
    finally:
        # Clean up
        if backend_process:
            print("\n🛑 Stopping backend server...")
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
