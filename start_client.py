#!/usr/bin/env python3
"""
Simple script to start the advanced chat client.
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_backend():
    """Check if backend is running."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def check_client_dependencies():
    """Check if client dependencies are installed."""
    print("🔍 Checking client dependencies...")
    
    required_packages = [
        ('tkinter', 'tkinter'),
        ('PIL', 'Pillow'),
        ('pygame', 'pygame'),
        ('cv2', 'opencv-python'),
        ('pyaudio', 'pyaudio'),
        ('plyer', 'plyer'),
        ('requests', 'requests')
    ]
    
    missing_packages = []
    
    for import_name, package_name in required_packages:
        try:
            if import_name == 'tkinter':
                import tkinter
            elif import_name == 'PIL':
                from PIL import Image, ImageTk
            elif import_name == 'pygame':
                import pygame
            elif import_name == 'cv2':
                import cv2
            elif import_name == 'pyaudio':
                import pyaudio
            elif import_name == 'plyer':
                from plyer import notification
            elif import_name == 'requests':
                import requests
            
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name} - Missing")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with:")
        for package in missing_packages:
            print(f"  pip install {package}")
        return False
    
    print("✅ All client dependencies available")
    return True

def start_client():
    """Start the chat client."""
    print("🖥️ Starting Advanced Chat Client...")
    
    # Get directories
    script_dir = Path(__file__).parent
    client_dir = script_dir / "client"
    
    # Check if client directory exists
    if not client_dir.exists():
        print(f"❌ Client directory not found: {client_dir}")
        return False
    
    # Check if main.py exists
    main_py = client_dir / "main.py"
    if not main_py.exists():
        print(f"❌ Client main.py not found: {main_py}")
        return False
    
    print(f"📁 Client directory: {client_dir}")
    print(f"📄 Main file: {main_py}")
    
    try:
        # Start the client
        print("🔄 Starting chat client...")
        
        cmd = [sys.executable, "main.py"]
        
        print(f"🔧 Command: {' '.join(cmd)}")
        print(f"📂 Working directory: {client_dir}")
        print("=" * 50)
        
        # Start the client
        process = subprocess.run(cmd, cwd=client_dir)
        
        return process.returncode == 0
        
    except KeyboardInterrupt:
        print("\n👋 Chat client closed by user")
        return True
    except Exception as e:
        print(f"❌ Error starting client: {e}")
        return False

def main():
    """Main function."""
    print("💬 Advanced Chat Client Starter")
    print("=" * 40)
    
    # Check if backend is running
    print("🔍 Checking backend connection...")
    if check_backend():
        print("✅ Backend is running at http://localhost:8001")
    else:
        print("❌ Backend is not running!")
        print("Please start the backend first:")
        print("  python start_backend.py")
        print("\nOr manually:")
        print("  cd backend")
        print("  python -m uvicorn app.main:app --host 0.0.0.0 --port 8001")
        return
    
    # Check dependencies
    if not check_client_dependencies():
        print("\n❌ Please install missing dependencies first")
        return
    
    # Start client
    print("\n🚀 Starting chat client...")
    print("📱 Advanced WhatsApp-like interface will open")
    print("🔑 Login with: alice / testpass123 or bob / testpass123")
    print("=" * 50)
    
    success = start_client()
    
    if success:
        print("✅ Chat client started successfully")
    else:
        print("❌ Failed to start chat client")

if __name__ == "__main__":
    main()
