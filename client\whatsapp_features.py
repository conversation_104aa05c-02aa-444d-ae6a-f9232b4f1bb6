import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import json
import os
import time

class WhatsAppFeatures:
    """Enhanced WhatsApp-like features for the chat application."""
    
    def __init__(self, parent_window):
        self.parent = parent_window
        self.typing_timer = None
        self.typing_users = set()
        self.message_status = {}  # Track message delivery status
        self.dark_mode = False
        
        # Colors for different themes
        self.light_theme = {
            'primary': '#075E54',
            'secondary': '#128C7E', 
            'accent': '#25D366',
            'background': '#ECE5DD',
            'sidebar': '#FFFFFF',
            'message_sent': '#DCF8C6',
            'message_received': '#FFFFFF',
            'text': '#000000',
            'text_secondary': '#667781',
            'online': '#25D366',
            'offline': '#8696A0',
            'typing': '#FFA500'
        }
        
        self.dark_theme = {
            'primary': '#2A2F32',
            'secondary': '#1F2428', 
            'accent': '#00A884',
            'background': '#0B141A',
            'sidebar': '#202C33',
            'message_sent': '#005C4B',
            'message_received': '#202C33',
            'text': '#E9EDEF',
            'text_secondary': '#8696A0',
            'online': '#00A884',
            'offline': '#667781',
            'typing': '#FFA500'
        }
        
        self.current_theme = self.light_theme
    
    def toggle_dark_mode(self):
        """Toggle between light and dark mode."""
        self.dark_mode = not self.dark_mode
        self.current_theme = self.dark_theme if self.dark_mode else self.light_theme
        self.apply_theme()
    
    def apply_theme(self):
        """Apply the current theme to all UI elements."""
        if hasattr(self.parent, 'main_frame'):
            self.update_widget_colors(self.parent.main_frame)
    
    def update_widget_colors(self, widget):
        """Recursively update widget colors."""
        try:
            widget_class = widget.winfo_class()
            
            if widget_class == 'Frame':
                widget.config(bg=self.current_theme['background'])
            elif widget_class == 'Label':
                widget.config(bg=self.current_theme['background'], fg=self.current_theme['text'])
            elif widget_class == 'Button':
                widget.config(bg=self.current_theme['accent'], fg='white')
            elif widget_class == 'Entry':
                widget.config(bg=self.current_theme['sidebar'], fg=self.current_theme['text'])
            elif widget_class == 'Text':
                widget.config(bg=self.current_theme['background'], fg=self.current_theme['text'])
            
            # Recursively update children
            for child in widget.winfo_children():
                self.update_widget_colors(child)
        except:
            pass
    
    def add_message_status_indicators(self, message_frame, message_data):
        """Add WhatsApp-style message status indicators."""
        status = message_data.get('status', 'sent')
        is_sent = message_data.get('sender_id') == self.parent.user_info['id']
        
        if is_sent:
            status_frame = tk.Frame(message_frame, bg=self.current_theme['message_sent'])
            status_frame.pack(side=tk.RIGHT, padx=5)
            
            # Status indicators (checkmarks)
            if status == 'sent':
                status_label = tk.Label(status_frame, text="✓", 
                                      fg='#999999', bg=self.current_theme['message_sent'],
                                      font=('Arial', 10))
            elif status == 'delivered':
                status_label = tk.Label(status_frame, text="✓✓", 
                                      fg='#999999', bg=self.current_theme['message_sent'],
                                      font=('Arial', 10))
            elif status == 'read':
                status_label = tk.Label(status_frame, text="✓✓", 
                                      fg='#4FC3F7', bg=self.current_theme['message_sent'],
                                      font=('Arial', 10))
            else:
                status_label = tk.Label(status_frame, text="⏱", 
                                      fg='#999999', bg=self.current_theme['message_sent'],
                                      font=('Arial', 10))
            
            status_label.pack()
    
    def show_typing_indicator(self, username):
        """Show typing indicator for a user."""
        self.typing_users.add(username)
        self.update_typing_display()
    
    def hide_typing_indicator(self, username):
        """Hide typing indicator for a user."""
        self.typing_users.discard(username)
        self.update_typing_display()
    
    def update_typing_display(self):
        """Update the typing indicator display."""
        if hasattr(self.parent, 'chat_status_label'):
            if self.typing_users:
                if len(self.typing_users) == 1:
                    text = f"{list(self.typing_users)[0]} is typing..."
                elif len(self.typing_users) == 2:
                    text = f"{', '.join(self.typing_users)} are typing..."
                else:
                    text = f"{len(self.typing_users)} people are typing..."
                
                self.parent.chat_status_label.config(text=text, fg=self.current_theme['typing'])
            else:
                # Reset to normal status
                if hasattr(self.parent, 'current_room'):
                    if self.parent.current_room and self.parent.current_room.get('is_group', False):
                        status_text = f"Group • {self.parent.current_room.get('member_count', 0)} members"
                    else:
                        status_text = "Online"  # Could be enhanced with real online status
                    self.parent.chat_status_label.config(text=status_text, fg=self.current_theme['text_secondary'])
    
    def start_typing_indicator(self):
        """Start typing indicator when user types."""
        if hasattr(self.parent, 'current_room') and self.parent.current_room:
            # Send typing start to API
            def send_typing():
                try:
                    self.parent.api_client._make_request(
                        "POST", 
                        f"/chat-rooms/{self.parent.current_room['id']}/typing/start"
                    )
                except:
                    pass
            
            threading.Thread(target=send_typing, daemon=True).start()
            
            # Reset timer
            if self.typing_timer:
                self.typing_timer.cancel()
            
            # Auto-stop typing after 3 seconds
            self.typing_timer = threading.Timer(3.0, self.stop_typing_indicator)
            self.typing_timer.start()
    
    def stop_typing_indicator(self):
        """Stop typing indicator."""
        if hasattr(self.parent, 'current_room') and self.parent.current_room:
            # Send typing stop to API
            def send_stop_typing():
                try:
                    self.parent.api_client._make_request(
                        "POST", 
                        f"/chat-rooms/{self.parent.current_room['id']}/typing/stop"
                    )
                except:
                    pass
            
            threading.Thread(target=send_stop_typing, daemon=True).start()
    
    def add_emoji_picker(self, parent_frame):
        """Add emoji picker button."""
        emoji_button = tk.Button(parent_frame, text="😊", font=('Arial', 14),
                                bg=self.current_theme['sidebar'], relief=tk.FLAT,
                                command=self.show_emoji_picker)
        emoji_button.pack(side=tk.LEFT, padx=5)
        return emoji_button
    
    def show_emoji_picker(self):
        """Show emoji picker popup."""
        emoji_window = tk.Toplevel(self.parent.parent)
        emoji_window.title("Emojis")
        emoji_window.geometry("300x200")
        emoji_window.configure(bg=self.current_theme['background'])
        
        # Common emojis
        emojis = [
            "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇",
            "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚",
            "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩",
            "🥳", "😏", "😒", "😞", "😔", "😟", "😕", "🙁", "☹️", "😣",
            "😖", "😫", "😩", "🥺", "😢", "😭", "😤", "😠", "😡", "🤬",
            "👍", "👎", "👌", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉",
            "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔"
        ]
        
        # Create emoji grid
        row = 0
        col = 0
        for emoji in emojis:
            btn = tk.Button(emoji_window, text=emoji, font=('Arial', 16),
                           bg=self.current_theme['sidebar'], relief=tk.FLAT,
                           command=lambda e=emoji: self.insert_emoji(e, emoji_window))
            btn.grid(row=row, column=col, padx=2, pady=2)
            
            col += 1
            if col >= 10:
                col = 0
                row += 1
    
    def insert_emoji(self, emoji, window):
        """Insert emoji into message input."""
        if hasattr(self.parent, 'message_entry'):
            current_text = self.parent.message_var.get()
            self.parent.message_var.set(current_text + emoji)
        window.destroy()
    
    def add_file_sharing_button(self, parent_frame):
        """Add file sharing button."""
        file_button = tk.Button(parent_frame, text="📎", font=('Arial', 14),
                               bg=self.current_theme['sidebar'], relief=tk.FLAT,
                               command=self.show_file_options)
        file_button.pack(side=tk.LEFT, padx=5)
        return file_button
    
    def show_file_options(self):
        """Show file sharing options."""
        file_menu = tk.Menu(self.parent.parent, tearoff=0)
        file_menu.configure(bg=self.current_theme['sidebar'], fg=self.current_theme['text'])
        
        file_menu.add_command(label="📷 Camera", command=self.open_camera)
        file_menu.add_command(label="🖼️ Gallery", command=self.select_image)
        file_menu.add_command(label="📄 Document", command=self.select_document)
        file_menu.add_command(label="🎵 Audio", command=self.select_audio)
        file_menu.add_command(label="📍 Location", command=self.share_location)
        file_menu.add_command(label="👤 Contact", command=self.share_contact)
        
        # Show menu at cursor position
        try:
            file_menu.tk_popup(self.parent.parent.winfo_pointerx(), 
                              self.parent.parent.winfo_pointery())
        finally:
            file_menu.grab_release()
    
    def select_image(self):
        """Select and send image."""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.gif *.bmp")]
        )
        if file_path:
            self.send_file(file_path, "image")
    
    def select_document(self):
        """Select and send document."""
        file_path = filedialog.askopenfilename(
            title="Select Document",
            filetypes=[("All files", "*.*")]
        )
        if file_path:
            self.send_file(file_path, "document")
    
    def select_audio(self):
        """Select and send audio."""
        file_path = filedialog.askopenfilename(
            title="Select Audio",
            filetypes=[("Audio files", "*.mp3 *.wav *.ogg *.m4a")]
        )
        if file_path:
            self.send_file(file_path, "audio")
    
    def send_file(self, file_path, file_type):
        """Send file to current chat."""
        if not hasattr(self.parent, 'current_room') or not self.parent.current_room:
            messagebox.showwarning("Warning", "Please select a chat first")
            return
        
        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        # For now, just send a message indicating file sharing
        # In a real implementation, you'd upload the file to a server
        message = f"📎 {file_name} ({self.format_file_size(file_size)})"
        
        # Send as regular message for now
        if hasattr(self.parent, 'send_message'):
            self.parent.message_var.set(message)
            self.parent.send_message()
    
    def format_file_size(self, size_bytes):
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f}{size_names[i]}"
    
    def open_camera(self):
        """Open camera for photo/video."""
        messagebox.showinfo("Camera", "Camera feature would open here")
    
    def share_location(self):
        """Share current location."""
        messagebox.showinfo("Location", "Location sharing feature would work here")
    
    def share_contact(self):
        """Share contact information."""
        messagebox.showinfo("Contact", "Contact sharing feature would work here")
    
    def add_voice_message_button(self, parent_frame):
        """Add voice message recording button."""
        voice_button = tk.Button(parent_frame, text="🎤", font=('Arial', 14),
                                bg=self.current_theme['sidebar'], relief=tk.FLAT,
                                command=self.toggle_voice_recording)
        voice_button.pack(side=tk.RIGHT, padx=5)
        return voice_button
    
    def toggle_voice_recording(self):
        """Toggle voice message recording."""
        if hasattr(self.parent, 'voice_recorder') and self.parent.voice_recorder:
            try:
                self.parent.voice_recorder.start_recording()
            except Exception as e:
                print(f"Voice recording error: {e}")
                messagebox.showerror("Voice Recording", f"Voice recording failed: {e}")
        else:
            messagebox.showinfo("Voice Message", "Voice recording not available (PyAudio not installed)")
    
    def handle_websocket_message(self, message):
        """Handle enhanced WebSocket messages."""
        msg_type = message.get('type')
        data = message.get('data', {})
        
        if msg_type == 'typing_start':
            username = data.get('username')
            if username and username != self.parent.user_info['username']:
                self.show_typing_indicator(username)
        
        elif msg_type == 'typing_stop':
            username = data.get('username')
            if username:
                self.hide_typing_indicator(username)
        
        elif msg_type == 'message_read':
            message_id = data.get('message_id')
            if message_id:
                self.update_message_status(message_id, 'read')
        
        elif msg_type == 'message':
            # Mark message as delivered for sender
            message_id = data.get('id')
            if message_id:
                self.update_message_status(message_id, 'delivered')
    
    def update_message_status(self, message_id, status):
        """Update message status in UI."""
        self.message_status[message_id] = status
        # In a real implementation, you'd update the UI to show the new status
