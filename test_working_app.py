#!/usr/bin/env python3
"""
Quick test to verify the chat application is working properly.
"""

import requests
import json
import time

def test_app():
    """Test the application functionality."""
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 Testing Chat Application")
    print("=" * 40)
    
    # Test 1: Health check
    print("1. Testing backend health...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Backend is healthy")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test 2: Login with existing user
    print("\n2. Testing login with existing user...")
    login_data = {
        "username": "testuser1750744580",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{base_url}/auth/login", json=login_data)
        if response.status_code == 200:
            print("✅ Login successful")
            token_data = response.json()
            access_token = token_data["access_token"]
            headers = {"Authorization": f"Bearer {access_token}"}
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test 3: Get chat rooms
    print("\n3. Testing chat rooms...")
    try:
        response = requests.get(f"{base_url}/chat-rooms", headers=headers)
        if response.status_code == 200:
            rooms = response.json()
            print(f"✅ Found {len(rooms)} chat rooms")
            if rooms:
                test_room = rooms[0]
                print(f"   Using room: {test_room['name']} (ID: {test_room['id']})")
            else:
                print("   No rooms found, will use General Chat")
                test_room = {"id": 4, "name": "General Chat"}
        else:
            print(f"❌ Chat rooms failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat rooms error: {e}")
        return False
    
    # Test 4: Send a message
    print("\n4. Testing message sending...")
    message_data = {
        "content": f"🧪 Test message from API at {time.strftime('%H:%M:%S')}",
        "message_type": "text"
    }
    
    try:
        response = requests.post(f"{base_url}/chat-rooms/{test_room['id']}/messages", 
                               json=message_data, headers=headers)
        if response.status_code == 200:
            print("✅ Message sent successfully!")
            message_response = response.json()
            print(f"   Message ID: {message_response['id']}")
            print(f"   Content: {message_response['content']}")
        else:
            print(f"❌ Message sending failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Message sending error: {e}")
        return False
    
    # Test 5: Get messages
    print("\n5. Testing message retrieval...")
    try:
        response = requests.get(f"{base_url}/chat-rooms/{test_room['id']}/messages", 
                               headers=headers)
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ Retrieved {len(messages)} messages")
            if messages:
                latest = messages[-1]
                print(f"   Latest: {latest['content'][:50]}...")
        else:
            print(f"❌ Message retrieval failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Message retrieval error: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("🎉 ALL TESTS PASSED!")
    print("\n✅ The chat application is working properly!")
    print("✅ Backend API is functional")
    print("✅ Authentication works")
    print("✅ Messages can be sent and received")
    print("\n📱 You can now run the client:")
    print("   python start_dev.py")
    print("   OR")
    print("   cd client && python main.py")
    
    return True

if __name__ == "__main__":
    success = test_app()
    if not success:
        print("\n❌ Some tests failed!")
        exit(1)
    else:
        print("\n🚀 Ready to use the chat application!")
        exit(0)
