#!/usr/bin/env python3
"""
Comprehensive startup script for the fully functional chat application.
This script ensures everything is properly configured and working.
"""

import os
import sys
import subprocess
import time
import threading
import requests
from pathlib import Path

def check_backend_health(port=8003):
    """Check if backend is healthy."""
    try:
        response = requests.get(f"http://127.0.0.1:{port}/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def start_backend(port=8003):
    """Start the backend server."""
    backend_dir = Path(__file__).parent / "backend"
    
    print(f"🚀 Starting backend server on port {port}...")
    
    if os.name == 'nt':  # Windows
        cmd = [
            "python", "-c", 
            f"import uvicorn; uvicorn.run('app.main:app', host='127.0.0.1', port={port}, log_level='info')"
        ]
        process = subprocess.Popen(cmd, cwd=backend_dir, 
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
    else:  # Unix/Linux/Mac
        cmd = ["python3", "-c", 
               f"import uvicorn; uvicorn.run('app.main:app', host='127.0.0.1', port={port}, log_level='info')"]
        process = subprocess.Popen(cmd, cwd=backend_dir)
    
    # Wait for backend to be ready
    print("⏳ Waiting for backend to start...")
    for i in range(30):
        if check_backend_health(port):
            print("✅ Backend server is ready!")
            return process
        time.sleep(1)
        if i % 5 == 0:
            print(f"   Still waiting... ({i+1}/30)")
    
    print("❌ Backend failed to start within 30 seconds")
    return None

def update_client_config(port=8003):
    """Update client configuration."""
    env_file = Path(__file__).parent / "client" / ".env"
    
    print(f"⚙️ Updating client configuration for port {port}...")
    
    try:
        with open(env_file, 'w') as f:
            f.write(f"# API Configuration\n")
            f.write(f"API_BASE_URL=http://127.0.0.1:{port}\n")
            f.write(f"\n# For production deployment, change to your deployed backend URL\n")
            f.write(f"# API_BASE_URL=https://your-app.onrender.com\n")
        print("✅ Client configuration updated")
        return True
    except Exception as e:
        print(f"❌ Failed to update client config: {e}")
        return False

def test_api_functionality(port=8003):
    """Test basic API functionality."""
    base_url = f"http://127.0.0.1:{port}"
    
    print("🧪 Testing API functionality...")
    
    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health endpoint working")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False
    
    # Test user registration with a test user
    test_user = {
        "username": f"testuser_{int(time.time())}",
        "email": f"test_{int(time.time())}@example.com", 
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{base_url}/auth/register", json=test_user, timeout=5)
        if response.status_code == 200:
            print("✅ User registration working")
        else:
            print(f"❌ User registration failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ User registration error: {e}")
        return False
    
    print("✅ API functionality test passed")
    return True

def start_client():
    """Start the client application."""
    client_dir = Path(__file__).parent / "client"
    
    print("🖥️ Starting client application...")
    
    if os.name == 'nt':  # Windows
        cmd = ["python", "main.py"]
    else:  # Unix/Linux/Mac
        cmd = ["python3", "main.py"]
    
    try:
        subprocess.run(cmd, cwd=client_dir)
    except KeyboardInterrupt:
        print("\n👋 Client application closed by user")
    except Exception as e:
        print(f"❌ Client error: {e}")

def main():
    """Main startup function."""
    print("💬 Fully Functional Chat Application Startup")
    print("=" * 50)
    
    # Find available port
    port = 8003
    for test_port in range(8003, 8010):
        if not check_backend_health(test_port):
            port = test_port
            break
    
    print(f"🔍 Using port {port}")
    
    # Update client configuration
    if not update_client_config(port):
        print("❌ Failed to update client configuration")
        return 1
    
    # Check if backend is already running
    if check_backend_health(port):
        print("✅ Backend is already running")
        backend_process = None
    else:
        # Start backend
        backend_process = start_backend(port)
        if not backend_process:
            print("\n❌ Failed to start backend server")
            print("Please check:")
            print("1. Python dependencies are installed (pip install -r backend/requirements.txt)")
            print(f"2. No other service is using port {port}")
            print("3. Database is accessible")
            return 1
    
    # Test API functionality
    if not test_api_functionality(port):
        print("\n❌ API functionality test failed")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 Backend is ready and fully functional!")
    print("📱 Starting client application...")
    print("\n💡 How to use:")
    print("1. Login with existing user (e.g., 'ali', 'bob', 'charlie') or register new user")
    print("2. Password for existing users: 'password123'")
    print("3. Click on 'General Chat' to join the main chat room")
    print("4. Click on 'Contacts' tab to start private chats")
    print("5. Type messages and press Enter to send")
    print("=" * 50)
    
    try:
        # Start client
        start_client()
    finally:
        # Clean up
        if backend_process:
            print("\n🛑 Stopping backend server...")
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                backend_process.kill()
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
