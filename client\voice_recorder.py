import tkinter as tk
from tkinter import ttk, messagebox
import threading
import wave
import pyaudio
import tempfile
import os
from datetime import datetime
from typing import Callable, Optional
import time

class VoiceRecorder:
    """Voice recording component for chat messages."""
    
    def __init__(self, parent, on_recording_complete: Callable[[str, float], None]):
        self.parent = parent
        self.on_recording_complete = on_recording_complete
        
        # Audio settings
        self.chunk = 1024
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 44100
        
        # Recording state
        self.is_recording = False
        self.audio = None
        self.stream = None
        self.frames = []
        self.start_time = None
        self.recording_thread = None
        
        # UI elements
        self.recording_window = None
        self.time_label = None
        self.waveform_canvas = None
        self.record_button = None
        self.stop_button = None
        self.cancel_button = None
        self.send_button = None
        
        # Timer
        self.timer_after_id = None
        
        self.setup_audio()
    
    def setup_audio(self):
        """Initialize audio system."""
        try:
            self.audio = pyaudio.PyAudio()
            print("✅ Audio system initialized successfully")
        except Exception as e:
            print(f"❌ Audio initialization failed: {e}")
            self.audio = None
    
    def show_recording_dialog(self):
        """Show the voice recording dialog."""
        if self.recording_window:
            return
        
        # Create recording window
        self.recording_window = tk.Toplevel(self.parent)
        self.recording_window.title("Voice Message")
        self.recording_window.geometry("400x300")
        self.recording_window.resizable(False, False)
        self.recording_window.transient(self.parent)
        self.recording_window.grab_set()
        
        # Center the window
        self.recording_window.update_idletasks()
        x = (self.recording_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.recording_window.winfo_screenheight() // 2) - (300 // 2)
        self.recording_window.geometry(f"400x300+{x}+{y}")
        
        # Handle window close
        self.recording_window.protocol("WM_DELETE_WINDOW", self.cancel_recording)
        
        self.setup_recording_ui()
    
    def setup_recording_ui(self):
        """Set up the recording UI."""
        main_frame = ttk.Frame(self.recording_window, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Voice Message", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Recording time display
        self.time_label = ttk.Label(main_frame, text="00:00", 
                                   font=("Arial", 24, "bold"))
        self.time_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # Waveform visualization
        self.waveform_canvas = tk.Canvas(main_frame, width=360, height=80, 
                                        bg='black', highlightthickness=1,
                                        highlightbackground='gray')
        self.waveform_canvas.grid(row=2, column=0, columnspan=3, pady=(0, 20))
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="Press record to start", 
                                     foreground="gray")
        self.status_label.grid(row=3, column=0, columnspan=3, pady=(0, 20))
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3)
        
        # Record button
        self.record_button = ttk.Button(button_frame, text="🎤 Record", 
                                       command=self.start_recording)
        self.record_button.grid(row=0, column=0, padx=5)
        
        # Stop button
        self.stop_button = ttk.Button(button_frame, text="⏹ Stop", 
                                     command=self.stop_recording, state="disabled")
        self.stop_button.grid(row=0, column=1, padx=5)
        
        # Cancel button
        self.cancel_button = ttk.Button(button_frame, text="❌ Cancel", 
                                       command=self.cancel_recording)
        self.cancel_button.grid(row=0, column=2, padx=5)
        
        # Send button
        self.send_button = ttk.Button(button_frame, text="📤 Send", 
                                     command=self.send_recording, state="disabled")
        self.send_button.grid(row=0, column=3, padx=5)
    
    def start_recording(self):
        """Start voice recording."""
        if not self.audio:
            messagebox.showerror("Error", "Audio system not available")
            return
        
        try:
            self.stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.rate,
                input=True,
                frames_per_buffer=self.chunk
            )
            
            self.is_recording = True
            self.frames = []
            self.start_time = time.time()
            
            # Update UI
            self.record_button.config(state="disabled")
            self.stop_button.config(state="normal")
            self.status_label.config(text="Recording...", foreground="red")
            
            # Start recording thread
            self.recording_thread = threading.Thread(target=self._record_audio, daemon=True)
            self.recording_thread.start()
            
            # Start timer
            self.update_timer()
            
        except Exception as e:
            messagebox.showerror("Recording Error", f"Failed to start recording: {e}")
            self.is_recording = False
    
    def _record_audio(self):
        """Record audio in background thread."""
        try:
            while self.is_recording and self.stream:
                data = self.stream.read(self.chunk, exception_on_overflow=False)
                self.frames.append(data)
                
                # Update waveform visualization
                self.parent.after(0, self.update_waveform, data)
                
        except Exception as e:
            self.parent.after(0, lambda: messagebox.showerror("Recording Error", str(e)))
    
    def update_waveform(self, audio_data):
        """Update waveform visualization."""
        if not self.waveform_canvas:
            return
        
        # Simple waveform visualization
        import struct
        
        # Convert audio data to amplitude values
        try:
            audio_values = struct.unpack(f'{len(audio_data)//2}h', audio_data)
            
            # Calculate average amplitude
            if audio_values:
                avg_amplitude = sum(abs(val) for val in audio_values) / len(audio_values)
                normalized_amplitude = min(avg_amplitude / 32768.0, 1.0)
                
                # Draw waveform bar
                canvas_width = self.waveform_canvas.winfo_width()
                canvas_height = self.waveform_canvas.winfo_height()
                
                if canvas_width > 1:
                    bar_height = int(normalized_amplitude * canvas_height * 0.8)
                    x = (time.time() - self.start_time) * 20 % canvas_width
                    
                    # Clear old bars that are too far
                    self.waveform_canvas.create_rectangle(
                        x, canvas_height//2 - bar_height//2,
                        x + 2, canvas_height//2 + bar_height//2,
                        fill='lime', outline='lime'
                    )
        except:
            pass  # Ignore waveform errors
    
    def update_timer(self):
        """Update recording timer."""
        if self.is_recording and self.start_time:
            elapsed = time.time() - self.start_time
            minutes = int(elapsed // 60)
            seconds = int(elapsed % 60)
            self.time_label.config(text=f"{minutes:02d}:{seconds:02d}")
            
            # Auto-stop after 5 minutes
            if elapsed >= 300:  # 5 minutes
                self.stop_recording()
                return
            
            # Schedule next update
            self.timer_after_id = self.recording_window.after(100, self.update_timer)
    
    def stop_recording(self):
        """Stop voice recording."""
        if not self.is_recording:
            return
        
        self.is_recording = False
        
        # Stop timer
        if self.timer_after_id:
            self.recording_window.after_cancel(self.timer_after_id)
            self.timer_after_id = None
        
        # Stop audio stream
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
            self.stream = None
        
        # Update UI
        self.record_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.send_button.config(state="normal")
        self.status_label.config(text="Recording complete. Click Send to share.", 
                                foreground="green")
        
        # Calculate duration
        if self.start_time:
            self.recording_duration = time.time() - self.start_time
        else:
            self.recording_duration = 0
    
    def send_recording(self):
        """Send the recorded voice message."""
        if not self.frames:
            messagebox.showwarning("No Recording", "No audio recorded")
            return
        
        try:
            # Save recording to temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
            
            with wave.open(temp_file.name, 'wb') as wf:
                wf.setnchannels(self.channels)
                wf.setsampwidth(self.audio.get_sample_size(self.format))
                wf.setframerate(self.rate)
                wf.writeframes(b''.join(self.frames))
            
            # Call completion callback
            self.on_recording_complete(temp_file.name, self.recording_duration)
            
            # Close dialog
            self.close_dialog()
            
        except Exception as e:
            messagebox.showerror("Save Error", f"Failed to save recording: {e}")
    
    def cancel_recording(self):
        """Cancel recording and close dialog."""
        if self.is_recording:
            self.is_recording = False
            
            if self.timer_after_id:
                self.recording_window.after_cancel(self.timer_after_id)
            
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
        
        self.close_dialog()
    
    def close_dialog(self):
        """Close the recording dialog."""
        if self.recording_window:
            self.recording_window.destroy()
            self.recording_window = None
        
        # Clean up
        self.frames = []
        self.start_time = None
        self.timer_after_id = None
    
    def cleanup(self):
        """Clean up audio resources."""
        if self.is_recording:
            self.cancel_recording()
        
        if self.audio:
            self.audio.terminate()
            self.audio = None

# Voice message player component
class VoicePlayer:
    """Voice message player component."""
    
    def __init__(self, parent):
        self.parent = parent
        self.audio = None
        self.stream = None
        self.is_playing = False
        self.current_file = None
        
        self.setup_audio()
    
    def setup_audio(self):
        """Initialize audio system."""
        try:
            self.audio = pyaudio.PyAudio()
        except Exception as e:
            print(f"Audio player initialization failed: {e}")
    
    def play_voice_message(self, file_path: str, duration: float):
        """Play a voice message."""
        if self.is_playing:
            self.stop_playback()
            return
        
        try:
            with wave.open(file_path, 'rb') as wf:
                # Start playback in thread
                self.is_playing = True
                self.current_file = file_path
                
                def play_audio():
                    try:
                        stream = self.audio.open(
                            format=self.audio.get_format_from_width(wf.getsampwidth()),
                            channels=wf.getnchannels(),
                            rate=wf.getframerate(),
                            output=True
                        )
                        
                        data = wf.readframes(1024)
                        while data and self.is_playing:
                            stream.write(data)
                            data = wf.readframes(1024)
                        
                        stream.stop_stream()
                        stream.close()
                        
                    except Exception as e:
                        print(f"Playback error: {e}")
                    finally:
                        self.is_playing = False
                
                threading.Thread(target=play_audio, daemon=True).start()
                
        except Exception as e:
            messagebox.showerror("Playback Error", f"Failed to play voice message: {e}")
    
    def stop_playback(self):
        """Stop voice message playback."""
        self.is_playing = False
    
    def cleanup(self):
        """Clean up audio resources."""
        self.stop_playback()
        if self.audio:
            self.audio.terminate()
            self.audio = None
