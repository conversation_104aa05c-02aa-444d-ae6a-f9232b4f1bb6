#!/usr/bin/env python3
"""
🧪 TEST PRIVATE MESSAGING BETWEEN USERS
======================================

This script tests private messaging functionality to ensure messages
are properly sent and received between users.
"""

import requests
import time
import json
import asyncio
import websockets
import threading

def test_private_messaging():
    """Test private messaging between two users."""
    print("🧪 Testing Private Messaging Between Users")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test with two different users
    user1_credentials = {"username": "testuser1750744580", "password": "testpass123"}
    user2_credentials = {"username": "alice", "password": "testpass123"}
    
    # Login both users
    print("🔐 Logging in users...")
    
    # User 1 login
    response1 = requests.post(f"{base_url}/auth/login", json=user1_credentials)
    if response1.status_code != 200:
        print(f"❌ User 1 login failed: {response1.status_code}")
        return False
    
    user1_token = response1.json()["access_token"]
    user1_headers = {"Authorization": f"Bearer {user1_token}"}
    print("✅ User 1 logged in")
    
    # User 2 login
    response2 = requests.post(f"{base_url}/auth/login", json=user2_credentials)
    if response2.status_code != 200:
        print(f"❌ User 2 login failed: {response2.status_code}")
        return False
    
    user2_token = response2.json()["access_token"]
    user2_headers = {"Authorization": f"Bearer {user2_token}"}
    print("✅ User 2 logged in")
    
    # Get user info
    user1_info = requests.get(f"{base_url}/users", headers=user1_headers).json()
    user2_info = requests.get(f"{base_url}/users", headers=user2_headers).json()
    
    # Find each other's user IDs
    user1_id = None
    user2_id = None
    
    for user in user1_info:
        if user["username"] == "alice":
            user2_id = user["id"]
            break
    
    for user in user2_info:
        if user["username"] == "testuser1750744580":
            user1_id = user["id"]
            break
    
    if not user1_id or not user2_id:
        print("❌ Could not find user IDs")
        return False
    
    print(f"✅ Found users: User1 ID={user1_id}, User2 ID={user2_id}")
    
    # Create private chat from User 1 to User 2
    print("💬 Creating private chat...")
    
    response = requests.post(f"{base_url}/chat-rooms/private?target_user_id={user2_id}", 
                           headers=user1_headers)
    if response.status_code != 200:
        print(f"❌ Private chat creation failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False
    
    private_chat = response.json()
    chat_room_id = private_chat["id"]
    print(f"✅ Private chat created: {private_chat['name']} (ID: {chat_room_id})")
    
    # Send message from User 1
    print("📤 Sending message from User 1...")
    
    message_data = {
        "content": "🧪 Hello from User 1! This is a test private message.",
        "message_type": "text"
    }
    
    response = requests.post(f"{base_url}/chat-rooms/{chat_room_id}/messages", 
                           json=message_data, headers=user1_headers)
    if response.status_code != 200:
        print(f"❌ Message sending failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False
    
    sent_message = response.json()
    print(f"✅ Message sent: {sent_message['content']}")
    
    # Wait a moment for message to propagate
    time.sleep(2)
    
    # Check if User 2 can see the message
    print("📥 Checking if User 2 received the message...")
    
    response = requests.get(f"{base_url}/chat-rooms/{chat_room_id}/messages", 
                          headers=user2_headers)
    if response.status_code != 200:
        print(f"❌ Message retrieval failed: {response.status_code}")
        print(f"Response: {response.text}")
        return False
    
    messages = response.json()
    print(f"✅ User 2 can see {len(messages)} messages in the chat")
    
    # Check if our test message is there
    test_message_found = False
    for message in messages:
        if "test private message" in message.get("content", "").lower():
            test_message_found = True
            print(f"✅ Test message found: {message['content']}")
            break
    
    if not test_message_found:
        print("❌ Test message not found in User 2's messages")
        return False
    
    # Send reply from User 2
    print("📤 Sending reply from User 2...")
    
    reply_data = {
        "content": "🧪 Hello back from User 2! I received your message!",
        "message_type": "text"
    }
    
    response = requests.post(f"{base_url}/chat-rooms/{chat_room_id}/messages", 
                           json=reply_data, headers=user2_headers)
    if response.status_code != 200:
        print(f"❌ Reply sending failed: {response.status_code}")
        return False
    
    reply_message = response.json()
    print(f"✅ Reply sent: {reply_message['content']}")
    
    # Wait a moment
    time.sleep(2)
    
    # Check if User 1 can see the reply
    print("📥 Checking if User 1 received the reply...")
    
    response = requests.get(f"{base_url}/chat-rooms/{chat_room_id}/messages", 
                          headers=user1_headers)
    if response.status_code == 200:
        messages = response.json()
        print(f"✅ User 1 can see {len(messages)} messages in the chat")
        
        # Check if reply is there
        reply_found = False
        for message in messages:
            if "received your message" in message.get("content", "").lower():
                reply_found = True
                print(f"✅ Reply found: {message['content']}")
                break
        
        if reply_found:
            print("\n🎉 PRIVATE MESSAGING TEST PASSED!")
            print("✅ Users can create private chats")
            print("✅ Messages are sent successfully")
            print("✅ Messages are received by other users")
            print("✅ Two-way communication working")
            return True
        else:
            print("❌ Reply not found in User 1's messages")
            return False
    else:
        print(f"❌ Could not retrieve messages for User 1: {response.status_code}")
        return False

def show_instructions():
    """Show instructions for testing in the GUI."""
    print("\n📖 GUI TESTING INSTRUCTIONS:")
    print("=" * 40)
    print("1. Open the chat application")
    print("2. Login with: testuser1750744580 / testpass123")
    print("3. Click 'Contacts' tab")
    print("4. Click on 'alice' user")
    print("5. Send a message")
    print("6. Open another instance and login with: alice / testpass123")
    print("7. Check if the message appears")
    print("8. Send a reply")
    print("9. Check if both users can see all messages")

if __name__ == "__main__":
    try:
        success = test_private_messaging()
        
        if success:
            show_instructions()
            print("\n🎉 PRIVATE MESSAGING IS WORKING!")
        else:
            print("\n❌ Private messaging test failed")
        
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        import traceback
        traceback.print_exc()
