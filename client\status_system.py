import tkinter as tk
from tkinter import ttk, messagebox, filedialog, colorchooser
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import json
import os
from PIL import Image, ImageTk, ImageDraw, ImageFont
import time

class StatusManager:
    """WhatsApp-style status/stories system."""
    
    def __init__(self, parent, user_info: Dict[str, Any], api_client):
        self.parent = parent
        self.user_info = user_info
        self.api_client = api_client
        
        # Status data
        self.my_status = []
        self.contacts_status = []
        self.status_window = None
        self.viewer_window = None
        
        # Colors for text status
        self.status_colors = [
            '#075E54', '#128C7E', '#25D366', '#34495E', '#E74C3C',
            '#9B59B6', '#3498DB', '#1ABC9C', '#F39C12', '#E67E22'
        ]
    
    def show_status_window(self):
        """Show the status management window."""
        if self.status_window:
            self.status_window.lift()
            return
        
        self.status_window = tk.Toplevel(self.parent)
        self.status_window.title("Status Updates")
        self.status_window.geometry("600x700")
        self.status_window.configure(bg='#ECE5DD')
        
        # Header
        header_frame = tk.Frame(self.status_window, bg='#075E54', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="Status", bg='#075E54', fg='white', 
                font=('Arial', 18, 'bold')).pack(side=tk.LEFT, padx=20, pady=15)
        
        # Close button
        close_btn = tk.Button(header_frame, text="✕", bg='#075E54', fg='white',
                             relief=tk.FLAT, font=('Arial', 16), command=self.close_status_window)
        close_btn.pack(side=tk.RIGHT, padx=20, pady=15)
        
        # My Status section
        self.setup_my_status_section()
        
        # Recent updates section
        self.setup_recent_updates_section()
        
        # Load status data
        self.load_status_data()
    
    def setup_my_status_section(self):
        """Set up my status section."""
        my_status_frame = tk.Frame(self.status_window, bg='white')
        my_status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Header
        header = tk.Frame(my_status_frame, bg='white')
        header.pack(fill=tk.X, padx=15, pady=10)
        
        tk.Label(header, text="My Status", bg='white', fg='#075E54',
                font=('Arial', 14, 'bold')).pack(side=tk.LEFT)
        
        # My status item
        status_item_frame = tk.Frame(my_status_frame, bg='white')
        status_item_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # Avatar with add button
        avatar_frame = tk.Frame(status_item_frame, bg='#25D366', width=50, height=50)
        avatar_frame.pack(side=tk.LEFT, padx=(0, 15))
        avatar_frame.pack_propagate(False)
        
        # User initial
        user_initial = self.user_info['username'][0].upper()
        avatar_label = tk.Label(avatar_frame, text=user_initial, bg='#25D366', fg='white',
                               font=('Arial', 20, 'bold'))
        avatar_label.pack(expand=True)
        
        # Add status button overlay
        add_btn = tk.Button(avatar_frame, text="+", bg='#075E54', fg='white',
                           relief=tk.FLAT, font=('Arial', 12, 'bold'),
                           width=2, height=1, command=self.create_status)
        add_btn.place(relx=0.7, rely=0.7)
        
        # Status info
        info_frame = tk.Frame(status_item_frame, bg='white')
        info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        self.my_status_label = tk.Label(info_frame, text="Tap to add status update",
                                       bg='white', fg='#667781', font=('Arial', 12))
        self.my_status_label.pack(anchor=tk.W)
        
        self.my_status_time = tk.Label(info_frame, text="",
                                      bg='white', fg='#8696A0', font=('Arial', 10))
        self.my_status_time.pack(anchor=tk.W)
        
        # Make clickable
        for widget in [status_item_frame, info_frame, self.my_status_label]:
            widget.bind("<Button-1>", lambda e: self.create_status())
    
    def setup_recent_updates_section(self):
        """Set up recent status updates section."""
        updates_frame = tk.Frame(self.status_window, bg='white')
        updates_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Header
        header = tk.Frame(updates_frame, bg='white')
        header.pack(fill=tk.X, padx=15, pady=10)
        
        tk.Label(header, text="Recent updates", bg='white', fg='#075E54',
                font=('Arial', 14, 'bold')).pack(side=tk.LEFT)
        
        # Scrollable list
        canvas_frame = tk.Frame(updates_frame, bg='white')
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        self.status_canvas = tk.Canvas(canvas_frame, bg='white', highlightthickness=0)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.status_canvas.yview)
        self.status_scrollable_frame = tk.Frame(self.status_canvas, bg='white')
        
        self.status_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.status_canvas.configure(scrollregion=self.status_canvas.bbox("all"))
        )
        
        self.status_canvas.create_window((0, 0), window=self.status_scrollable_frame, anchor="nw")
        self.status_canvas.configure(yscrollcommand=scrollbar.set)
        
        self.status_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_status(self):
        """Show status creation options."""
        create_window = tk.Toplevel(self.status_window)
        create_window.title("Create Status")
        create_window.geometry("400x300")
        create_window.configure(bg='#075E54')
        create_window.transient(self.status_window)
        create_window.grab_set()
        
        # Center window
        create_window.update_idletasks()
        x = (create_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (create_window.winfo_screenheight() // 2) - (300 // 2)
        create_window.geometry(f"400x300+{x}+{y}")
        
        # Header
        header = tk.Label(create_window, text="Create Status Update", 
                         bg='#075E54', fg='white', font=('Arial', 16, 'bold'))
        header.pack(pady=20)
        
        # Options
        options_frame = tk.Frame(create_window, bg='#075E54')
        options_frame.pack(expand=True)
        
        # Text status
        text_btn = tk.Button(options_frame, text="📝 Text Status", 
                            bg='#25D366', fg='white', font=('Arial', 14, 'bold'),
                            relief=tk.FLAT, padx=30, pady=15,
                            command=lambda: [create_window.destroy(), self.create_text_status()])
        text_btn.pack(pady=10)
        
        # Photo status
        photo_btn = tk.Button(options_frame, text="📷 Photo Status", 
                             bg='#25D366', fg='white', font=('Arial', 14, 'bold'),
                             relief=tk.FLAT, padx=30, pady=15,
                             command=lambda: [create_window.destroy(), self.create_photo_status()])
        photo_btn.pack(pady=10)
        
        # Video status
        video_btn = tk.Button(options_frame, text="🎥 Video Status", 
                             bg='#25D366', fg='white', font=('Arial', 14, 'bold'),
                             relief=tk.FLAT, padx=30, pady=15,
                             command=lambda: [create_window.destroy(), self.create_video_status()])
        video_btn.pack(pady=10)
    
    def create_text_status(self):
        """Create a text-based status."""
        text_window = tk.Toplevel(self.status_window)
        text_window.title("Text Status")
        text_window.geometry("500x600")
        text_window.configure(bg='#075E54')
        text_window.transient(self.status_window)
        text_window.grab_set()
        
        # Center window
        text_window.update_idletasks()
        x = (text_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (text_window.winfo_screenheight() // 2) - (600 // 2)
        text_window.geometry(f"500x600+{x}+{y}")
        
        # Preview area
        preview_frame = tk.Frame(text_window, bg='#25D366', width=400, height=300)
        preview_frame.pack(pady=20)
        preview_frame.pack_propagate(False)
        
        # Text input
        text_var = tk.StringVar()
        text_entry = tk.Text(preview_frame, font=('Arial', 16, 'bold'), 
                            bg='#25D366', fg='white', relief=tk.FLAT,
                            wrap=tk.WORD, padx=20, pady=20)
        text_entry.pack(fill=tk.BOTH, expand=True)
        text_entry.insert(tk.END, "Type your status here...")
        text_entry.bind("<FocusIn>", lambda e: text_entry.delete(1.0, tk.END) if text_entry.get(1.0, tk.END).strip() == "Type your status here..." else None)
        
        # Color selection
        color_frame = tk.Frame(text_window, bg='#075E54')
        color_frame.pack(pady=10)
        
        tk.Label(color_frame, text="Background Color:", bg='#075E54', fg='white',
                font=('Arial', 12)).pack()
        
        colors_frame = tk.Frame(color_frame, bg='#075E54')
        colors_frame.pack(pady=10)
        
        selected_color = tk.StringVar(value='#25D366')
        
        for i, color in enumerate(self.status_colors):
            color_btn = tk.Button(colors_frame, bg=color, width=3, height=2,
                                 relief=tk.FLAT, command=lambda c=color: [
                                     selected_color.set(c),
                                     preview_frame.config(bg=c),
                                     text_entry.config(bg=c)
                                 ])
            color_btn.grid(row=i//5, column=i%5, padx=2, pady=2)
        
        # Buttons
        button_frame = tk.Frame(text_window, bg='#075E54')
        button_frame.pack(pady=20)
        
        cancel_btn = tk.Button(button_frame, text="Cancel", bg='#E74C3C', fg='white',
                              relief=tk.FLAT, padx=20, pady=10,
                              command=text_window.destroy)
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        post_btn = tk.Button(button_frame, text="Post Status", bg='#25D366', fg='white',
                            relief=tk.FLAT, padx=20, pady=10,
                            command=lambda: self.post_text_status(
                                text_entry.get(1.0, tk.END).strip(),
                                selected_color.get(),
                                text_window
                            ))
        post_btn.pack(side=tk.LEFT, padx=10)
    
    def create_photo_status(self):
        """Create a photo-based status."""
        file_path = filedialog.askopenfilename(
            title="Select Photo for Status",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.gif *.bmp")]
        )
        
        if file_path:
            self.post_media_status(file_path, "photo")
    
    def create_video_status(self):
        """Create a video-based status."""
        file_path = filedialog.askopenfilename(
            title="Select Video for Status",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv")]
        )
        
        if file_path:
            self.post_media_status(file_path, "video")
    
    def post_text_status(self, text: str, bg_color: str, window):
        """Post a text status."""
        if not text or text == "Type your status here...":
            messagebox.showwarning("Empty Status", "Please enter some text for your status")
            return
        
        status_data = {
            "type": "text",
            "content": text,
            "background_color": bg_color,
            "timestamp": datetime.now().isoformat()
        }
        
        self.save_status(status_data)
        window.destroy()
        messagebox.showinfo("Status Posted", "Your status has been posted!")
        self.refresh_my_status()
    
    def post_media_status(self, file_path: str, media_type: str):
        """Post a media status."""
        try:
            # Create thumbnail for preview
            thumbnail_path = None
            if media_type == "photo":
                thumbnail_path = self.create_image_thumbnail(file_path)

            status_data = {
                "type": media_type,
                "file_path": file_path,
                "thumbnail_path": thumbnail_path,
                "file_name": os.path.basename(file_path),
                "timestamp": datetime.now().isoformat()
            }

            self.save_status(status_data)
            messagebox.showinfo("Status Posted", f"Your {media_type} status has been posted!")
            self.refresh_my_status()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to post {media_type} status: {e}")

    def create_image_thumbnail(self, image_path: str) -> str:
        """Create thumbnail for image status."""
        try:
            from PIL import Image

            # Open and resize image
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Create thumbnail
                img.thumbnail((200, 200), Image.Resampling.LANCZOS)

                # Save thumbnail
                thumbnail_dir = os.path.join(os.path.dirname(__file__), "status_thumbnails")
                os.makedirs(thumbnail_dir, exist_ok=True)

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                thumbnail_path = os.path.join(thumbnail_dir, f"thumb_{timestamp}.jpg")

                img.save(thumbnail_path, "JPEG", quality=85)
                return thumbnail_path

        except Exception as e:
            print(f"Failed to create thumbnail: {e}")
            return None
    
    def save_status(self, status_data: Dict[str, Any]):
        """Save status to local storage."""
        # Add expiry time (24 hours)
        status_data["expires_at"] = (datetime.now() + timedelta(hours=24)).isoformat()
        status_data["user_id"] = self.user_info["id"]
        status_data["username"] = self.user_info["username"]
        
        # Save to file (in real app, this would be sent to server)
        status_file = os.path.join(os.path.dirname(__file__), "status_data.json")
        
        try:
            if os.path.exists(status_file):
                with open(status_file, 'r') as f:
                    all_status = json.load(f)
            else:
                all_status = []
            
            all_status.append(status_data)
            
            with open(status_file, 'w') as f:
                json.dump(all_status, f, indent=2)
                
        except Exception as e:
            print(f"Error saving status: {e}")
    
    def load_status_data(self):
        """Load status data from storage."""
        status_file = os.path.join(os.path.dirname(__file__), "status_data.json")
        
        try:
            if os.path.exists(status_file):
                with open(status_file, 'r') as f:
                    all_status = json.load(f)
                
                # Filter expired status
                current_time = datetime.now()
                valid_status = []
                
                for status in all_status:
                    expires_at = datetime.fromisoformat(status["expires_at"])
                    if expires_at > current_time:
                        valid_status.append(status)
                
                # Separate my status and others
                self.my_status = [s for s in valid_status if s["user_id"] == self.user_info["id"]]
                self.contacts_status = [s for s in valid_status if s["user_id"] != self.user_info["id"]]
                
                # Update display
                self.refresh_my_status()
                self.refresh_contacts_status()
                
        except Exception as e:
            print(f"Error loading status: {e}")
    
    def refresh_my_status(self):
        """Refresh my status display."""
        if self.my_status:
            latest_status = self.my_status[-1]
            timestamp = datetime.fromisoformat(latest_status["timestamp"])
            time_ago = self.get_time_ago(timestamp)
            
            if latest_status["type"] == "text":
                self.my_status_label.config(text=latest_status["content"][:30] + "...")
            else:
                self.my_status_label.config(text=f"{latest_status['type'].title()} status")
            
            self.my_status_time.config(text=time_ago)
        else:
            self.my_status_label.config(text="Tap to add status update")
            self.my_status_time.config(text="")
    
    def refresh_contacts_status(self):
        """Refresh contacts status display."""
        # Clear existing status items
        for widget in self.status_scrollable_frame.winfo_children():
            widget.destroy()
        
        # Group status by user
        user_status = {}
        for status in self.contacts_status:
            user_id = status["user_id"]
            if user_id not in user_status:
                user_status[user_id] = []
            user_status[user_id].append(status)
        
        # Create status items
        for user_id, statuses in user_status.items():
            latest_status = max(statuses, key=lambda s: s["timestamp"])
            self.create_status_item(latest_status, len(statuses))
    
    def create_status_item(self, status: Dict[str, Any], count: int):
        """Create a status item in the list."""
        item_frame = tk.Frame(self.status_scrollable_frame, bg='white')
        item_frame.pack(fill=tk.X, pady=5)
        
        # Avatar
        avatar_frame = tk.Frame(item_frame, bg='#25D366', width=50, height=50)
        avatar_frame.pack(side=tk.LEFT, padx=(0, 15))
        avatar_frame.pack_propagate(False)
        
        user_initial = status["username"][0].upper()
        avatar_label = tk.Label(avatar_frame, text=user_initial, bg='#25D366', fg='white',
                               font=('Arial', 20, 'bold'))
        avatar_label.pack(expand=True)
        
        # Status ring (indicates unviewed status)
        ring_canvas = tk.Canvas(item_frame, width=60, height=60, highlightthickness=0, bg='white')
        ring_canvas.place(x=-5, y=-5)
        ring_canvas.create_oval(5, 5, 55, 55, outline='#25D366', width=3)
        
        # Info
        info_frame = tk.Frame(item_frame, bg='white')
        info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        name_label = tk.Label(info_frame, text=status["username"], bg='white', fg='black',
                             font=('Arial', 12, 'bold'))
        name_label.pack(anchor=tk.W)
        
        timestamp = datetime.fromisoformat(status["timestamp"])
        time_ago = self.get_time_ago(timestamp)
        time_label = tk.Label(info_frame, text=time_ago, bg='white', fg='#8696A0',
                             font=('Arial', 10))
        time_label.pack(anchor=tk.W)
        
        # Click to view
        for widget in [item_frame, info_frame, name_label, time_label]:
            widget.bind("<Button-1>", lambda e, s=status: self.view_status(s))
    
    def view_status(self, status: Dict[str, Any]):
        """View a status update."""
        if self.viewer_window:
            self.viewer_window.destroy()
        
        self.viewer_window = tk.Toplevel(self.status_window)
        self.viewer_window.title("Status Viewer")
        self.viewer_window.geometry("400x600")
        self.viewer_window.configure(bg='black')
        self.viewer_window.transient(self.status_window)
        
        # Header
        header_frame = tk.Frame(self.viewer_window, bg='black')
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # User info
        user_label = tk.Label(header_frame, text=status["username"], bg='black', fg='white',
                             font=('Arial', 14, 'bold'))
        user_label.pack(side=tk.LEFT)
        
        # Time
        timestamp = datetime.fromisoformat(status["timestamp"])
        time_ago = self.get_time_ago(timestamp)
        time_label = tk.Label(header_frame, text=time_ago, bg='black', fg='#8696A0',
                             font=('Arial', 10))
        time_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Close button
        close_btn = tk.Button(header_frame, text="✕", bg='black', fg='white',
                             relief=tk.FLAT, command=self.viewer_window.destroy)
        close_btn.pack(side=tk.RIGHT)
        
        # Content area
        content_frame = tk.Frame(self.viewer_window, bg=status.get("background_color", "black"))
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        if status["type"] == "text":
            # Text status
            text_label = tk.Label(content_frame, text=status["content"],
                                 bg=status.get("background_color", "black"), fg='white',
                                 font=('Arial', 18, 'bold'), wraplength=350, justify=tk.CENTER)
            text_label.pack(expand=True)
        elif status["type"] == "photo":
            # Photo status with preview
            self.show_photo_status(content_frame, status)
        elif status["type"] == "video":
            # Video status
            self.show_video_status(content_frame, status)
        else:
            # Other media status
            media_label = tk.Label(content_frame, text=f"{status['type'].title()} Status",
                                  bg='black', fg='white', font=('Arial', 14))
            media_label.pack(expand=True)

    def show_photo_status(self, parent_frame: tk.Frame, status: Dict[str, Any]):
        """Show photo status with image preview."""
        try:
            from PIL import Image, ImageTk

            # Try to load thumbnail first, then original
            image_path = status.get("thumbnail_path") or status.get("file_path")

            if image_path and os.path.exists(image_path):
                # Load and display image
                image = Image.open(image_path)

                # Resize to fit viewer
                max_size = (350, 400)
                image.thumbnail(max_size, Image.Resampling.LANCZOS)

                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(image)

                # Display image
                image_label = tk.Label(parent_frame, image=photo, bg='black')
                image_label.image = photo  # Keep reference
                image_label.pack(expand=True)

                # Add caption if any
                caption = status.get("caption", "")
                if caption:
                    caption_label = tk.Label(parent_frame, text=caption, bg='black', fg='white',
                                           font=('Arial', 12), wraplength=350)
                    caption_label.pack(pady=10)
            else:
                # Fallback if image not found
                error_label = tk.Label(parent_frame, text="📷 Photo Status\n(Image not available)",
                                     bg='black', fg='white', font=('Arial', 14))
                error_label.pack(expand=True)

        except Exception as e:
            print(f"Error showing photo status: {e}")
            error_label = tk.Label(parent_frame, text="📷 Photo Status\n(Preview error)",
                                 bg='black', fg='white', font=('Arial', 14))
            error_label.pack(expand=True)

    def show_video_status(self, parent_frame: tk.Frame, status: Dict[str, Any]):
        """Show video status."""
        # Video thumbnail or placeholder
        video_frame = tk.Frame(parent_frame, bg='black')
        video_frame.pack(expand=True, fill=tk.BOTH)

        # Video icon
        video_icon = tk.Label(video_frame, text="🎥", bg='black', fg='white',
                             font=('Arial', 48))
        video_icon.pack(expand=True)

        # Video info
        file_name = status.get("file_name", "Video")
        info_label = tk.Label(video_frame, text=f"Video Status\n{file_name}",
                             bg='black', fg='white', font=('Arial', 12))
        info_label.pack()

        # Play button
        play_btn = tk.Button(video_frame, text="▶️ Play Video", bg='#25D366', fg='white',
                            relief=tk.FLAT, font=('Arial', 10, 'bold'),
                            command=lambda: self.play_video_status(status))
        play_btn.pack(pady=10)

    def play_video_status(self, status: Dict[str, Any]):
        """Play video status."""
        file_path = status.get("file_path")
        if file_path and os.path.exists(file_path):
            try:
                import subprocess
                import platform

                if platform.system() == 'Windows':
                    os.startfile(file_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.run(['open', file_path])
                else:  # Linux
                    subprocess.run(['xdg-open', file_path])
            except Exception as e:
                messagebox.showerror("Error", f"Cannot play video: {e}")
        else:
            messagebox.showerror("Error", "Video file not found")
    
    def get_time_ago(self, timestamp: datetime) -> str:
        """Get human-readable time ago string."""
        now = datetime.now()
        diff = now - timestamp
        
        if diff.days > 0:
            return f"{diff.days}d ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours}h ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes}m ago"
        else:
            return "Just now"
    
    def close_status_window(self):
        """Close the status window."""
        if self.viewer_window:
            self.viewer_window.destroy()
        if self.status_window:
            self.status_window.destroy()
            self.status_window = None
