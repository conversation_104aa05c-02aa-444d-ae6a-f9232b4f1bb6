#!/usr/bin/env python3
"""
Test script to verify backend functionality.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the app directory to the path
sys.path.insert(0, str(Path(__file__).parent))

async def test_backend():
    """Test backend functionality."""
    try:
        print("🧪 Testing backend imports...")
        from app.main import app
        from app.database import init_db, AsyncSessionLocal
        from app.auth import create_user, authenticate_user
        from app.users import UserCreate
        
        print("✅ All imports successful!")
        
        print("🗄️ Testing database connection...")
        await init_db()
        print("✅ Database initialized!")
        
        print("👤 Testing user creation...")
        async with AsyncSessionLocal() as db:
            # Try to create a test user
            test_user = UserCreate(
                username="testuser123",
                email="<EMAIL>",
                password="testpass123"
            )
            
            try:
                user = await create_user(db, test_user)
                print(f"✅ Test user created: {user.username}")
            except Exception as e:
                if "UNIQUE constraint failed" in str(e):
                    print("✅ Test user already exists (expected)")
                else:
                    print(f"❌ User creation error: {e}")
                    return False
        
        print("🔐 Testing authentication...")
        async with AsyncSessionLocal() as db:
            auth_result = await authenticate_user(db, "testuser123", "testpass123")
            if auth_result:
                print("✅ Authentication successful!")
            else:
                print("❌ Authentication failed!")
                return False
        
        print("🎉 All backend tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_backend())
    sys.exit(0 if result else 1)
