import os
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
from fastapi import Fast<PERSON><PERSON>, Depends, HTTPException, status, UploadFile, File, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from .database import get_db, init_db, AsyncSessionLocal
from .auth import (
    authenticate_user, create_user, get_current_active_user, 
    create_access_token, create_refresh_token, verify_token,
    update_user_online_status, Token, RefreshTokenRequest
)
from .users import (
    User, UserCreate, UserResponse, UserLogin, UserUpdate, UserProfile,
    ChatRoom, ChatRoomCreate, ChatRoomResponse, ChatMember, ChatMemberResponse,
    ContactCreate, ContactResponse, StatusUpdateCreate, StatusUpdateResponse
)
from .messages import (
    Message, Message<PERSON>reate, MessageResponse, Message<PERSON>pdate, MessageWithSender,
    MessageType, FileUploadResponse, MessageReactionCreate, MessageReactionResponse,
    MessageReply, MessageForward, MessageStatus, MessageReadReceipt, TypingIndicator
)
from .websocket_manager import manager, start_cleanup_task

# Create FastAPI app
app = FastAPI(
    title="Real-Time Chat API",
    description="A real-time chat application with WebSocket support",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()

# Startup event
@app.on_event("startup")
async def startup_event():
    await init_db()
    start_cleanup_task()  # Start the WebSocket cleanup task
    print("Chat application started successfully!")

# Health check endpoint
@app.get("/")
async def root():
    return {"message": "Real-Time Chat API is running!"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "chat-api"}

# Authentication endpoints
@app.post("/auth/register", response_model=UserResponse)
async def register(user: UserCreate, db: AsyncSession = Depends(get_db)):
    """Register a new user."""
    try:
        db_user = await create_user(db, user)
        return UserResponse.from_orm(db_user)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

@app.post("/auth/login", response_model=Token)
async def login(user_credentials: UserLogin, db: AsyncSession = Depends(get_db)):
    """Authenticate user and return JWT tokens."""
    user = await authenticate_user(db, user_credentials.username, user_credentials.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Update user online status
    await update_user_online_status(db, user.id, True)
    
    # Create tokens
    access_token = create_access_token(
        data={"sub": user.username, "user_id": user.id}
    )
    refresh_token = create_refresh_token(
        data={"sub": user.username, "user_id": user.id}
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer"
    )

@app.post("/auth/refresh", response_model=Token)
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """Refresh access token using refresh token."""
    token_data = verify_token(refresh_request.refresh_token, "refresh")
    if not token_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Create new tokens
    access_token = create_access_token(
        data={"sub": token_data.username, "user_id": token_data.user_id}
    )
    refresh_token = create_refresh_token(
        data={"sub": token_data.username, "user_id": token_data.user_id}
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer"
    )

@app.post("/auth/logout")
async def logout(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Logout user and update online status."""
    await update_user_online_status(db, current_user.id, False)
    return {"message": "Successfully logged out"}

# User endpoints
@app.get("/users/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """Get current user information."""
    return UserResponse.from_orm(current_user)

@app.put("/users/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update current user information."""
    update_data = user_update.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(current_user, field, value)
    
    await db.commit()
    await db.refresh(current_user)
    return UserResponse.from_orm(current_user)

@app.get("/users", response_model=List[UserResponse])
async def get_users(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get list of users."""
    result = await db.execute(
        select(User)
        .where(User.is_active == True)
        .offset(skip)
        .limit(limit)
    )
    users = result.scalars().all()
    return [UserResponse.from_orm(user) for user in users]

# Chat room endpoints
@app.post("/chat-rooms", response_model=ChatRoomResponse)
async def create_chat_room(
    room: ChatRoomCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new chat room."""
    db_room = ChatRoom(
        name=room.name,
        description=room.description,
        is_group=room.is_group,
        created_by=current_user.id
    )

    db.add(db_room)
    await db.commit()
    await db.refresh(db_room)

    # Add creator as admin member
    db_member = ChatMember(
        chat_room_id=db_room.id,
        user_id=current_user.id,
        is_admin=True
    )
    db.add(db_member)
    await db.commit()

    return ChatRoomResponse.from_orm(db_room)

@app.post("/chat-rooms/private", response_model=ChatRoomResponse)
async def create_private_chat(
    target_user_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create or get a private chat between two users."""
    try:
        # Check if target user exists
        target_result = await db.execute(select(User).where(User.id == target_user_id))
        target_user = target_result.scalar_one_or_none()
        if not target_user:
            raise HTTPException(status_code=404, detail="Target user not found")

        # Check if private chat already exists between these users
        existing_chat_result = await db.execute(
            select(ChatRoom)
            .join(ChatMember, ChatRoom.id == ChatMember.chat_room_id)
            .where(
                and_(
                    ChatRoom.is_group == False,
                    ChatMember.user_id.in_([current_user.id, target_user_id])
                )
            )
            .group_by(ChatRoom.id)
            .having(func.count(ChatMember.user_id) == 2)
        )
        existing_chat = existing_chat_result.scalar_one_or_none()

        if existing_chat:
            # Return existing private chat
            return ChatRoomResponse.from_orm(existing_chat)

        # Create new private chat
        chat_name = f"Chat with {target_user.username}"
        db_room = ChatRoom(
            name=chat_name,
            description=f"Private chat between {current_user.username} and {target_user.username}",
            is_group=False,
            created_by=current_user.id
        )

        db.add(db_room)
        await db.commit()
        await db.refresh(db_room)

        # Add both users as members
        for user_id in [current_user.id, target_user_id]:
            db_member = ChatMember(
                chat_room_id=db_room.id,
                user_id=user_id,
                is_admin=False
            )
            db.add(db_member)

        await db.commit()

        # Add both users to the WebSocket room immediately
        await manager.join_room(current_user.id, db_room.id)
        await manager.join_room(target_user_id, db_room.id)

        # Notify both users via WebSocket
        await manager.send_personal_message(target_user_id, {
            "type": "new_private_chat",
            "data": {
                "chat_room_id": db_room.id,
                "chat_room_name": f"Chat with {current_user.username}",
                "other_user": {
                    "id": current_user.id,
                    "username": current_user.username
                }
            }
        })

        await manager.send_personal_message(current_user.id, {
            "type": "new_private_chat",
            "data": {
                "chat_room_id": db_room.id,
                "chat_room_name": f"Chat with {target_user.username}",
                "other_user": {
                    "id": target_user_id,
                    "username": target_user.username
                }
            }
        })

        return ChatRoomResponse.from_orm(db_room)

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error creating private chat: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create private chat: {str(e)}"
        )

@app.get("/chat-rooms", response_model=List[ChatRoomResponse])
async def get_user_chat_rooms(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get chat rooms for current user."""
    result = await db.execute(
        select(ChatRoom)
        .join(ChatMember)
        .where(ChatMember.user_id == current_user.id)
        .options(selectinload(ChatRoom.members))
    )
    rooms = result.scalars().all()

    room_responses = []
    for room in rooms:
        room_response = ChatRoomResponse.from_orm(room)
        room_response.member_count = len(room.members)
        room_responses.append(room_response)

    return room_responses

@app.post("/chat-rooms/{room_id}/join")
async def join_chat_room(
    room_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Join a chat room."""
    # Check if room exists
    result = await db.execute(select(ChatRoom).where(ChatRoom.id == room_id))
    room = result.scalar_one_or_none()
    if not room:
        raise HTTPException(status_code=404, detail="Chat room not found")

    # Check if already a member
    result = await db.execute(
        select(ChatMember).where(
            and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
        )
    )
    existing_member = result.scalar_one_or_none()
    if existing_member:
        raise HTTPException(status_code=400, detail="Already a member of this chat room")

    # Add as member
    db_member = ChatMember(
        chat_room_id=room_id,
        user_id=current_user.id,
        is_admin=False
    )
    db.add(db_member)
    await db.commit()

    return {"message": "Successfully joined chat room"}

@app.delete("/chat-rooms/{room_id}/leave")
async def leave_chat_room(
    room_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Leave a chat room."""
    result = await db.execute(
        select(ChatMember).where(
            and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
        )
    )
    member = result.scalar_one_or_none()
    if not member:
        raise HTTPException(status_code=404, detail="Not a member of this chat room")

    await db.delete(member)
    await db.commit()

    return {"message": "Successfully left chat room"}

@app.get("/chat-rooms/{room_id}/members", response_model=List[ChatMemberResponse])
async def get_chat_room_members(
    room_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get members of a chat room."""
    # Verify user is a member
    result = await db.execute(
        select(ChatMember).where(
            and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
        )
    )
    user_member = result.scalar_one_or_none()
    if not user_member:
        raise HTTPException(status_code=403, detail="Not a member of this chat room")

    # Get all members
    result = await db.execute(
        select(ChatMember, User)
        .join(User)
        .where(ChatMember.chat_room_id == room_id)
    )
    members_data = result.all()

    members = []
    for member, user in members_data:
        member_response = ChatMemberResponse(
            id=member.id,
            user_id=user.id,
            username=user.username,
            is_admin=member.is_admin,
            joined_at=member.joined_at
        )
        members.append(member_response)

    return members

# Message endpoints
@app.post("/chat-rooms/{room_id}/messages", response_model=MessageResponse)
async def send_message(
    room_id: int,
    message: MessageCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Send a message to a chat room."""
    try:
        # Verify user is a member of the chat room
        result = await db.execute(
            select(ChatMember).where(
                and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
            )
        )
        member = result.scalar_one_or_none()
        if not member:
            raise HTTPException(status_code=403, detail="Not a member of this chat room")

        # Create message
        db_message = Message(
            chat_room_id=room_id,
            sender_id=current_user.id,
            content=message.content,
            message_type=message.message_type
        )

        db.add(db_message)
        await db.commit()
        await db.refresh(db_message)

        # Create response manually to avoid from_orm issues
        response = MessageResponse(
            id=db_message.id,
            content=db_message.content,
            message_type=db_message.message_type,
            chat_room_id=db_message.chat_room_id,
            sender_id=db_message.sender_id,
            sender_username=current_user.username,
            status=MessageStatus.SENT,
            is_edited=db_message.is_edited or False,
            is_deleted=db_message.is_deleted or False,
            forward_count=db_message.forward_count or 0,
            created_at=db_message.created_at,
            reactions=[],
            read_by=[]
        )

        # Create delivery receipts for all room members
        members_result = await db.execute(
            select(ChatMember).where(ChatMember.chat_room_id == room_id)
        )
        members = members_result.scalars().all()

        for member in members:
            if member.user_id != current_user.id:  # Don't create receipt for sender
                receipt = MessageReadReceipt(
                    message_id=db_message.id,
                    user_id=member.user_id,
                    status=MessageStatus.DELIVERED,
                    delivered_at=db_message.created_at
                )
                db.add(receipt)

        await db.commit()

        # Broadcast message via WebSocket
        message_data = {
            "id": db_message.id,
            "chat_room_id": room_id,
            "sender_id": current_user.id,
            "sender_username": current_user.username,
            "content": message.content,
            "message_type": message.message_type,
            "status": MessageStatus.DELIVERED,
            "created_at": db_message.created_at.isoformat()
        }

        await manager.broadcast_to_room(room_id, {
            "type": "message",
            "data": message_data
        })

        return response

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error sending message: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send message: {str(e)}"
        )

@app.get("/chat-rooms/{room_id}/messages", response_model=List[MessageWithSender])
async def get_messages(
    room_id: int,
    skip: int = 0,
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get messages from a chat room."""
    # Verify user is a member
    result = await db.execute(
        select(ChatMember).where(
            and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
        )
    )
    member = result.scalar_one_or_none()
    if not member:
        raise HTTPException(status_code=403, detail="Not a member of this chat room")

    # Get messages with sender info
    result = await db.execute(
        select(Message, User)
        .join(User, Message.sender_id == User.id)
        .where(Message.chat_room_id == room_id)
        .order_by(Message.created_at.desc())
        .offset(skip)
        .limit(limit)
    )
    messages_data = result.all()

    messages = []
    for message, sender in messages_data:
        message_response = MessageWithSender(
            id=message.id,
            chat_room_id=message.chat_room_id,
            sender_id=message.sender_id,
            sender_username=sender.username,
            sender_avatar=sender.avatar_url,
            content=message.content,
            message_type=message.message_type,
            file_url=message.file_url,
            file_name=message.file_name,
            file_size=message.file_size,
            status=message.status or MessageStatus.SENT,
            is_edited=message.is_edited or False,
            edited_at=message.edited_at,
            is_deleted=message.is_deleted or False,
            reply_to_id=message.reply_to_id,
            forwarded_from_id=message.forwarded_from_id,
            forward_count=message.forward_count or 0,
            created_at=message.created_at,
            reactions=[],
            read_by=[]
        )
        messages.append(message_response)

    return list(reversed(messages))  # Return in chronological order

@app.post("/messages/{message_id}/read")
async def mark_message_as_read(
    message_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Mark a message as read."""
    try:
        # Check if message exists and user has access
        result = await db.execute(
            select(Message, ChatMember)
            .join(ChatMember, Message.chat_room_id == ChatMember.chat_room_id)
            .where(
                and_(
                    Message.id == message_id,
                    ChatMember.user_id == current_user.id
                )
            )
        )
        message_access = result.first()

        if not message_access:
            raise HTTPException(status_code=404, detail="Message not found or access denied")

        message = message_access[0]

        # Update or create read receipt
        receipt_result = await db.execute(
            select(MessageReadReceipt).where(
                and_(
                    MessageReadReceipt.message_id == message_id,
                    MessageReadReceipt.user_id == current_user.id
                )
            )
        )
        receipt = receipt_result.scalar_one_or_none()

        if receipt:
            receipt.status = MessageStatus.READ
            receipt.read_at = datetime.now()
        else:
            receipt = MessageReadReceipt(
                message_id=message_id,
                user_id=current_user.id,
                status=MessageStatus.READ,
                delivered_at=datetime.now(),
                read_at=datetime.now()
            )
            db.add(receipt)

        await db.commit()

        # Broadcast read receipt to sender
        await manager.send_personal_message(message.sender_id, {
            "type": "message_read",
            "data": {
                "message_id": message_id,
                "reader_id": current_user.id,
                "reader_username": current_user.username,
                "read_at": receipt.read_at.isoformat()
            }
        })

        return {"status": "success", "message": "Message marked as read"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error marking message as read: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark message as read: {str(e)}"
        )

@app.post("/chat-rooms/{room_id}/typing/start")
async def start_typing(
    room_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Start typing indicator."""
    try:
        # Verify user is a member
        result = await db.execute(
            select(ChatMember).where(
                and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
            )
        )
        member = result.scalar_one_or_none()
        if not member:
            raise HTTPException(status_code=403, detail="Not a member of this chat room")

        # Broadcast typing indicator
        await manager.broadcast_to_room(room_id, {
            "type": "typing_start",
            "data": {
                "user_id": current_user.id,
                "username": current_user.username,
                "chat_room_id": room_id
            }
        })

        return {"status": "success"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error starting typing: {e}")
        raise HTTPException(status_code=500, detail="Failed to start typing")

@app.post("/chat-rooms/{room_id}/typing/stop")
async def stop_typing(
    room_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Stop typing indicator."""
    try:
        # Verify user is a member
        result = await db.execute(
            select(ChatMember).where(
                and_(ChatMember.chat_room_id == room_id, ChatMember.user_id == current_user.id)
            )
        )
        member = result.scalar_one_or_none()
        if not member:
            raise HTTPException(status_code=403, detail="Not a member of this chat room")

        # Broadcast stop typing indicator
        await manager.broadcast_to_room(room_id, {
            "type": "typing_stop",
            "data": {
                "user_id": current_user.id,
                "username": current_user.username,
                "chat_room_id": room_id
            }
        })

        return {"status": "success"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"Error stopping typing: {e}")
        raise HTTPException(status_code=500, detail="Failed to stop typing")

@app.put("/messages/{message_id}", response_model=MessageResponse)
async def edit_message(
    message_id: int,
    message_update: MessageUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Edit a message."""
    result = await db.execute(select(Message).where(Message.id == message_id))
    message = result.scalar_one_or_none()

    if not message:
        raise HTTPException(status_code=404, detail="Message not found")

    if message.sender_id != current_user.id:
        raise HTTPException(status_code=403, detail="Can only edit your own messages")

    message.content = message_update.content
    message.is_edited = True
    message.edited_at = datetime.utcnow()

    await db.commit()
    await db.refresh(message)

    return MessageResponse.from_orm(message)

@app.delete("/messages/{message_id}")
async def delete_message(
    message_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a message."""
    result = await db.execute(select(Message).where(Message.id == message_id))
    message = result.scalar_one_or_none()

    if not message:
        raise HTTPException(status_code=404, detail="Message not found")

    if message.sender_id != current_user.id:
        raise HTTPException(status_code=403, detail="Can only delete your own messages")

    await db.delete(message)
    await db.commit()

    return {"message": "Message deleted successfully"}

# WebSocket endpoint
@app.websocket("/ws/chat")
async def websocket_endpoint(websocket: WebSocket, token: str = None):
    """WebSocket endpoint for real-time chat."""
    if not token:
        await websocket.close(code=1008, reason="Token required")
        return

    # Verify token
    token_data = verify_token(token)
    if not token_data:
        await websocket.close(code=1008, reason="Invalid token")
        return

    user_id = token_data.user_id

    try:
        # Connect user
        await manager.connect(websocket, user_id)

        # Get user's chat rooms and join them
        async with AsyncSessionLocal() as db:
            result = await db.execute(
                select(ChatMember.chat_room_id)
                .where(ChatMember.user_id == user_id)
            )
            room_ids = [row[0] for row in result.all()]

            for room_id in room_ids:
                await manager.join_room(user_id, room_id)

        # Listen for messages
        while True:
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)

                message_type = message_data.get("type")

                if message_type == "typing_start":
                    room_id = message_data.get("room_id")
                    if room_id:
                        await manager.handle_typing_indicator(user_id, room_id, True)

                elif message_type == "typing_stop":
                    room_id = message_data.get("room_id")
                    if room_id:
                        await manager.handle_typing_indicator(user_id, room_id, False)

                elif message_type == "join_room":
                    room_id = message_data.get("room_id")
                    if room_id:
                        await manager.join_room(user_id, room_id)

                elif message_type == "refresh_rooms":
                    # Refresh user's room memberships
                    async with AsyncSessionLocal() as db:
                        result = await db.execute(
                            select(ChatMember.chat_room_id)
                            .where(ChatMember.user_id == user_id)
                        )
                        room_ids = [row[0] for row in result.all()]

                        for room_id in room_ids:
                            await manager.join_room(user_id, room_id)

                elif message_type == "leave_room":
                    room_id = message_data.get("room_id")
                    if room_id:
                        await manager.leave_room(user_id, room_id)

                elif message_type == "call_invitation":
                    # Handle call invitation
                    call_data = message_data.get("data", {})
                    target_user_id = call_data.get("target_user_id")
                    if target_user_id:
                        await manager.send_personal_message(target_user_id, {
                            "type": "call_invitation",
                            "data": call_data
                        })

                elif message_type == "call_accepted":
                    # Handle call acceptance
                    call_data = message_data.get("data", {})
                    caller_id = call_data.get("caller_id")
                    if caller_id:
                        await manager.send_personal_message(caller_id, {
                            "type": "call_accepted",
                            "data": call_data
                        })

                elif message_type == "call_declined":
                    # Handle call decline
                    call_data = message_data.get("data", {})
                    caller_id = call_data.get("caller_id")
                    if caller_id:
                        await manager.send_personal_message(caller_id, {
                            "type": "call_declined",
                            "data": call_data
                        })

                elif message_type == "call_ended":
                    # Handle call end
                    call_data = message_data.get("data", {})
                    target_user_id = call_data.get("target_user_id")
                    if target_user_id:
                        await manager.send_personal_message(target_user_id, {
                            "type": "call_ended",
                            "data": call_data
                        })

                elif message_type == "ping":
                    # Respond to ping to keep connection alive
                    await manager.send_personal_message(user_id, {
                        "type": "pong",
                        "timestamp": datetime.utcnow().isoformat()
                    })

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await manager.send_personal_message(user_id, {
                    "type": "error",
                    "message": "Invalid JSON format"
                })
            except Exception as e:
                await manager.send_personal_message(user_id, {
                    "type": "error",
                    "message": f"Error processing message: {str(e)}"
                })

    except Exception as e:
        print(f"WebSocket error for user {user_id}: {e}")
    finally:
        await manager.disconnect(user_id)

# File upload endpoints
@app.post("/upload", response_model=FileUploadResponse)
async def upload_file(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user)
):
    """Upload a file."""
    # Create uploads directory if it doesn't exist
    upload_dir = "uploads"
    os.makedirs(upload_dir, exist_ok=True)

    # Generate unique filename
    import uuid
    file_extension = os.path.splitext(file.filename)[1] if file.filename else ""
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join(upload_dir, unique_filename)

    try:
        # Save file
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Determine message type
        message_type = MessageType.FILE
        if file.content_type and file.content_type.startswith("image/"):
            message_type = MessageType.IMAGE

        return FileUploadResponse(
            file_url=f"/files/{unique_filename}",
            file_name=file.filename or unique_filename,
            file_size=len(content),
            message_type=message_type
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"File upload failed: {str(e)}"
        )

@app.get("/files/{filename}")
async def get_file(filename: str):
    """Serve uploaded files."""
    from fastapi.responses import FileResponse
    file_path = os.path.join("uploads", filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(file_path)

# Message Reactions endpoints
@app.post("/messages/{message_id}/reactions", response_model=MessageReactionResponse)
async def add_message_reaction(
    message_id: int,
    reaction: MessageReactionCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Add a reaction to a message."""
    from .messages import MessageReaction

    # Check if message exists and user has access
    result = await db.execute(
        select(Message, ChatMember)
        .join(ChatMember, Message.chat_room_id == ChatMember.chat_room_id)
        .where(
            and_(
                Message.id == message_id,
                ChatMember.user_id == current_user.id
            )
        )
    )
    message_access = result.first()

    if not message_access:
        raise HTTPException(status_code=404, detail="Message not found or access denied")

    # Check if user already reacted with this emoji
    result = await db.execute(
        select(MessageReaction).where(
            and_(
                MessageReaction.message_id == message_id,
                MessageReaction.user_id == current_user.id,
                MessageReaction.emoji == reaction.emoji
            )
        )
    )
    existing_reaction = result.scalar_one_or_none()

    if existing_reaction:
        # Remove existing reaction
        await db.delete(existing_reaction)
        await db.commit()
        return {"message": "Reaction removed"}

    # Add new reaction
    db_reaction = MessageReaction(
        message_id=message_id,
        user_id=current_user.id,
        emoji=reaction.emoji
    )

    db.add(db_reaction)
    await db.commit()
    await db.refresh(db_reaction)

    # Broadcast reaction via WebSocket
    await manager.broadcast_to_room(message_access[0].chat_room_id, {
        "type": "message_reaction",
        "data": {
            "message_id": message_id,
            "user_id": current_user.id,
            "username": current_user.username,
            "emoji": reaction.emoji,
            "action": "add"
        }
    })

    return MessageReactionResponse(
        id=db_reaction.id,
        message_id=message_id,
        user_id=current_user.id,
        username=current_user.username,
        emoji=reaction.emoji,
        created_at=db_reaction.created_at
    )

@app.post("/messages/{message_id}/reply", response_model=MessageResponse)
async def reply_to_message(
    message_id: int,
    reply: MessageReply,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Reply to a message."""
    # Check if original message exists and user has access
    result = await db.execute(
        select(Message, ChatMember)
        .join(ChatMember, Message.chat_room_id == ChatMember.chat_room_id)
        .where(
            and_(
                Message.id == message_id,
                ChatMember.user_id == current_user.id
            )
        )
    )
    message_access = result.first()

    if not message_access:
        raise HTTPException(status_code=404, detail="Message not found or access denied")

    original_message = message_access[0]

    # Create reply message
    db_message = Message(
        chat_room_id=original_message.chat_room_id,
        sender_id=current_user.id,
        content=reply.content,
        message_type=MessageType.TEXT,
        reply_to_id=message_id
    )

    db.add(db_message)
    await db.commit()
    await db.refresh(db_message)

    # Broadcast reply via WebSocket
    message_data = {
        "id": db_message.id,
        "chat_room_id": original_message.chat_room_id,
        "sender_id": current_user.id,
        "sender_username": current_user.username,
        "content": reply.content,
        "message_type": MessageType.TEXT,
        "reply_to_id": message_id,
        "reply_to_content": original_message.content[:50] + "..." if len(original_message.content) > 50 else original_message.content,
        "created_at": db_message.created_at.isoformat()
    }

    await manager.broadcast_to_room(original_message.chat_room_id, {
        "type": "message",
        "data": message_data
    })

    return MessageResponse.from_orm(db_message)

@app.post("/messages/forward", response_model=List[MessageResponse])
async def forward_messages(
    forward_request: MessageForward,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Forward messages to another chat room."""
    # Check if user has access to target chat room
    result = await db.execute(
        select(ChatMember).where(
            and_(
                ChatMember.chat_room_id == forward_request.target_chat_room_id,
                ChatMember.user_id == current_user.id
            )
        )
    )
    target_access = result.scalar_one_or_none()

    if not target_access:
        raise HTTPException(status_code=403, detail="Access denied to target chat room")

    forwarded_messages = []

    for message_id in forward_request.message_ids:
        # Get original message
        result = await db.execute(
            select(Message, ChatMember)
            .join(ChatMember, Message.chat_room_id == ChatMember.chat_room_id)
            .where(
                and_(
                    Message.id == message_id,
                    ChatMember.user_id == current_user.id
                )
            )
        )
        message_access = result.first()

        if not message_access:
            continue  # Skip messages user doesn't have access to

        original_message = message_access[0]

        # Create forwarded message
        db_message = Message(
            chat_room_id=forward_request.target_chat_room_id,
            sender_id=current_user.id,
            content=original_message.content,
            message_type=original_message.message_type,
            file_url=original_message.file_url,
            file_name=original_message.file_name,
            file_size=original_message.file_size,
            forwarded_from_id=message_id,
            forward_count=original_message.forward_count + 1
        )

        db.add(db_message)
        forwarded_messages.append(db_message)

    await db.commit()

    # Refresh and broadcast forwarded messages
    for db_message in forwarded_messages:
        await db.refresh(db_message)

        message_data = {
            "id": db_message.id,
            "chat_room_id": forward_request.target_chat_room_id,
            "sender_id": current_user.id,
            "sender_username": current_user.username,
            "content": db_message.content,
            "message_type": db_message.message_type,
            "forwarded_from_id": db_message.forwarded_from_id,
            "forward_count": db_message.forward_count,
            "created_at": db_message.created_at.isoformat()
        }

        await manager.broadcast_to_room(forward_request.target_chat_room_id, {
            "type": "message",
            "data": message_data
        })

    return [MessageResponse.from_orm(msg) for msg in forwarded_messages]

# Contact Management endpoints
@app.post("/contacts", response_model=ContactResponse)
async def add_contact(
    contact: ContactCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Add a new contact."""
    from .users import Contact

    # Check if contact user exists
    result = await db.execute(select(User).where(User.id == contact.contact_user_id))
    contact_user = result.scalar_one_or_none()

    if not contact_user:
        raise HTTPException(status_code=404, detail="User not found")

    if contact_user.id == current_user.id:
        raise HTTPException(status_code=400, detail="Cannot add yourself as contact")

    # Check if contact already exists
    result = await db.execute(
        select(Contact).where(
            and_(
                Contact.user_id == current_user.id,
                Contact.contact_user_id == contact.contact_user_id
            )
        )
    )
    existing_contact = result.scalar_one_or_none()

    if existing_contact:
        raise HTTPException(status_code=400, detail="Contact already exists")

    # Create contact
    db_contact = Contact(
        user_id=current_user.id,
        contact_user_id=contact.contact_user_id,
        contact_name=contact.contact_name
    )

    db.add(db_contact)
    await db.commit()
    await db.refresh(db_contact)

    return ContactResponse(
        id=db_contact.id,
        contact_user_id=contact_user.id,
        contact_name=db_contact.contact_name,
        contact_username=contact_user.username,
        contact_full_name=contact_user.full_name,
        contact_avatar_url=contact_user.avatar_url,
        contact_status_message=contact_user.status_message,
        is_blocked=db_contact.is_blocked,
        is_favorite=db_contact.is_favorite,
        is_online=contact_user.is_online,
        last_seen=contact_user.last_seen,
        added_at=db_contact.added_at
    )

@app.get("/contacts", response_model=List[ContactResponse])
async def get_contacts(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's contacts."""
    from .users import Contact

    result = await db.execute(
        select(Contact, User)
        .join(User, Contact.contact_user_id == User.id)
        .where(Contact.user_id == current_user.id)
        .order_by(Contact.contact_name, User.full_name, User.username)
    )
    contacts_data = result.all()

    contacts = []
    for contact, contact_user in contacts_data:
        contact_response = ContactResponse(
            id=contact.id,
            contact_user_id=contact_user.id,
            contact_name=contact.contact_name,
            contact_username=contact_user.username,
            contact_full_name=contact_user.full_name,
            contact_avatar_url=contact_user.avatar_url,
            contact_status_message=contact_user.status_message,
            is_blocked=contact.is_blocked,
            is_favorite=contact.is_favorite,
            is_online=contact_user.is_online,
            last_seen=contact_user.last_seen,
            added_at=contact.added_at
        )
        contacts.append(contact_response)

    return contacts

# Status Updates endpoints
@app.post("/status", response_model=StatusUpdateResponse)
async def create_status_update(
    status: StatusUpdateCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new status update."""
    from .users import StatusUpdate
    from datetime import timedelta

    # Set expiration time (24 hours from now)
    expires_at = datetime.utcnow() + timedelta(hours=24)

    db_status = StatusUpdate(
        user_id=current_user.id,
        content=status.content,
        media_url=status.media_url,
        media_type=status.media_type,
        background_color=status.background_color,
        font_style=status.font_style,
        privacy_setting=status.privacy_setting,
        expires_at=expires_at
    )

    db.add(db_status)
    await db.commit()
    await db.refresh(db_status)

    return StatusUpdateResponse(
        id=db_status.id,
        user_id=current_user.id,
        username=current_user.username,
        user_avatar_url=current_user.avatar_url,
        content=db_status.content,
        media_url=db_status.media_url,
        media_type=db_status.media_type,
        background_color=db_status.background_color,
        font_style=db_status.font_style,
        privacy_setting=db_status.privacy_setting,
        view_count=db_status.view_count,
        expires_at=db_status.expires_at,
        created_at=db_status.created_at,
        is_viewed=False
    )

@app.get("/status", response_model=List[StatusUpdateResponse])
async def get_status_updates(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get status updates from contacts."""
    from .users import StatusUpdate, Contact, StatusView

    # Get status updates from contacts and own status
    result = await db.execute(
        select(StatusUpdate, User)
        .join(User, StatusUpdate.user_id == User.id)
        .join(Contact,
              or_(
                  and_(Contact.user_id == current_user.id, Contact.contact_user_id == StatusUpdate.user_id),
                  StatusUpdate.user_id == current_user.id
              ),
              isouter=True)
        .where(
            and_(
                StatusUpdate.expires_at > datetime.utcnow(),
                or_(
                    StatusUpdate.user_id == current_user.id,  # Own status
                    and_(Contact.user_id == current_user.id, Contact.is_blocked == False),  # Contact's status
                    StatusUpdate.privacy_setting == "all"  # Public status
                )
            )
        )
        .order_by(StatusUpdate.created_at.desc())
    )
    status_data = result.all()

    status_updates = []
    for status, user in status_data:
        # Check if current user viewed this status
        view_result = await db.execute(
            select(StatusView).where(
                and_(
                    StatusView.status_id == status.id,
                    StatusView.viewer_id == current_user.id
                )
            )
        )
        is_viewed = view_result.scalar_one_or_none() is not None

        status_response = StatusUpdateResponse(
            id=status.id,
            user_id=user.id,
            username=user.username,
            user_avatar_url=user.avatar_url,
            content=status.content,
            media_url=status.media_url,
            media_type=status.media_type,
            background_color=status.background_color,
            font_style=status.font_style,
            privacy_setting=status.privacy_setting,
            view_count=status.view_count,
            expires_at=status.expires_at,
            created_at=status.created_at,
            is_viewed=is_viewed
        )
        status_updates.append(status_response)

    return status_updates

@app.post("/status/{status_id}/view")
async def view_status_update(
    status_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Mark a status update as viewed."""
    from .users import StatusUpdate, StatusView

    # Check if status exists and is accessible
    result = await db.execute(
        select(StatusUpdate).where(StatusUpdate.id == status_id)
    )
    status = result.scalar_one_or_none()

    if not status:
        raise HTTPException(status_code=404, detail="Status not found")

    if status.expires_at <= datetime.utcnow():
        raise HTTPException(status_code=410, detail="Status has expired")

    # Check if already viewed
    result = await db.execute(
        select(StatusView).where(
            and_(
                StatusView.status_id == status_id,
                StatusView.viewer_id == current_user.id
            )
        )
    )
    existing_view = result.scalar_one_or_none()

    if existing_view:
        return {"message": "Status already viewed"}

    # Create view record
    db_view = StatusView(
        status_id=status_id,
        viewer_id=current_user.id
    )

    db.add(db_view)

    # Update view count
    status.view_count += 1

    await db.commit()

    return {"message": "Status viewed successfully"}
