import tkinter as tk
from tkinter import ttk, messagebox
import os
from typing import Dict, Any, Optional
from PIL import Image, ImageTk
import mimetypes
import subprocess
import platform

class FilePreviewManager:
    """Advanced file preview system for chat messages."""
    
    def __init__(self, parent):
        self.parent = parent
        self.preview_window = None
        
        # Supported file types
        self.image_types = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff'}
        self.video_types = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
        self.audio_types = {'.mp3', '.wav', '.ogg', '.m4a', '.flac', '.aac'}
        self.document_types = {'.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'}
        self.code_types = {'.py', '.js', '.html', '.css', '.java', '.cpp', '.c', '.php'}
        self.archive_types = {'.zip', '.rar', '.7z', '.tar', '.gz'}
    
    def show_file_preview(self, file_path: str, file_name: str, file_size: int):
        """Show preview for a file."""
        if not os.path.exists(file_path):
            messagebox.showerror("File Not Found", f"File {file_name} not found")
            return
        
        file_ext = os.path.splitext(file_name)[1].lower()
        
        if self.preview_window:
            self.preview_window.destroy()
        
        self.preview_window = tk.Toplevel(self.parent)
        self.preview_window.title(f"Preview: {file_name}")
        self.preview_window.geometry("800x600")
        self.preview_window.configure(bg='#F0F0F0')
        
        # Center window
        self.center_window()
        
        # Header with file info
        self.create_file_header(file_name, file_size, file_ext)
        
        # Preview content based on file type
        if file_ext in self.image_types:
            self.show_image_preview(file_path)
        elif file_ext in self.video_types:
            self.show_video_preview(file_path)
        elif file_ext in self.audio_types:
            self.show_audio_preview(file_path)
        elif file_ext in self.document_types:
            self.show_document_preview(file_path)
        elif file_ext in self.code_types:
            self.show_code_preview(file_path)
        else:
            self.show_generic_preview(file_path, file_ext)
        
        # Action buttons
        self.create_action_buttons(file_path)
    
    def center_window(self):
        """Center the preview window."""
        self.preview_window.update_idletasks()
        width = self.preview_window.winfo_width()
        height = self.preview_window.winfo_height()
        x = (self.preview_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.preview_window.winfo_screenheight() // 2) - (height // 2)
        self.preview_window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_file_header(self, file_name: str, file_size: int, file_ext: str):
        """Create file information header."""
        header_frame = tk.Frame(self.preview_window, bg='#075E54', height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # File icon
        icon_frame = tk.Frame(header_frame, bg='#075E54')
        icon_frame.pack(side=tk.LEFT, padx=20, pady=15)
        
        icon_text = self.get_file_icon(file_ext)
        icon_label = tk.Label(icon_frame, text=icon_text, bg='#075E54', fg='white',
                             font=('Arial', 24))
        icon_label.pack()
        
        # File info
        info_frame = tk.Frame(header_frame, bg='#075E54')
        info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, pady=15)
        
        # File name
        name_label = tk.Label(info_frame, text=file_name, bg='#075E54', fg='white',
                             font=('Arial', 14, 'bold'), anchor=tk.W)
        name_label.pack(fill=tk.X)
        
        # File details
        size_str = self.format_file_size(file_size)
        file_type = self.get_file_type_description(file_ext)
        details_text = f"{file_type} • {size_str}"
        
        details_label = tk.Label(info_frame, text=details_text, bg='#075E54', fg='#B0B0B0',
                                font=('Arial', 10), anchor=tk.W)
        details_label.pack(fill=tk.X)
        
        # Close button
        close_btn = tk.Button(header_frame, text="✕", bg='#075E54', fg='white',
                             relief=tk.FLAT, font=('Arial', 16),
                             command=self.close_preview)
        close_btn.pack(side=tk.RIGHT, padx=20, pady=15)
    
    def show_image_preview(self, file_path: str):
        """Show image preview."""
        try:
            # Load and resize image
            image = Image.open(file_path)
            
            # Calculate size to fit in preview area
            max_width, max_height = 700, 400
            image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(image)
            
            # Create preview frame
            preview_frame = tk.Frame(self.preview_window, bg='white')
            preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # Image label
            image_label = tk.Label(preview_frame, image=photo, bg='white')
            image_label.image = photo  # Keep reference
            image_label.pack(expand=True)
            
            # Image info
            info_text = f"Dimensions: {image.size[0]} × {image.size[1]} pixels"
            info_label = tk.Label(preview_frame, text=info_text, bg='white', fg='#667781',
                                 font=('Arial', 10))
            info_label.pack(pady=10)
            
        except Exception as e:
            self.show_error_preview(f"Cannot preview image: {e}")
    
    def show_video_preview(self, file_path: str):
        """Show video preview."""
        preview_frame = tk.Frame(self.preview_window, bg='black')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Video placeholder
        video_icon = tk.Label(preview_frame, text="🎥", bg='black', fg='white',
                             font=('Arial', 64))
        video_icon.pack(expand=True)
        
        # Video info
        info_label = tk.Label(preview_frame, text="Video file\nClick 'Open' to play with default player",
                             bg='black', fg='white', font=('Arial', 12), justify=tk.CENTER)
        info_label.pack(pady=20)
    
    def show_audio_preview(self, file_path: str):
        """Show audio preview."""
        preview_frame = tk.Frame(self.preview_window, bg='#1E1E1E')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Audio icon
        audio_icon = tk.Label(preview_frame, text="🎵", bg='#1E1E1E', fg='white',
                             font=('Arial', 64))
        audio_icon.pack(expand=True)
        
        # Audio controls placeholder
        controls_frame = tk.Frame(preview_frame, bg='#1E1E1E')
        controls_frame.pack(pady=20)
        
        play_btn = tk.Button(controls_frame, text="▶️ Play", bg='#25D366', fg='white',
                            relief=tk.FLAT, padx=20, pady=10,
                            command=lambda: self.open_with_default_app(file_path))
        play_btn.pack()
        
        info_label = tk.Label(preview_frame, text="Audio file\nClick play to listen",
                             bg='#1E1E1E', fg='white', font=('Arial', 12), justify=tk.CENTER)
        info_label.pack()
    
    def show_document_preview(self, file_path: str):
        """Show document preview."""
        preview_frame = tk.Frame(self.preview_window, bg='white')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Document icon
        doc_icon = tk.Label(preview_frame, text="📄", bg='white', fg='#075E54',
                           font=('Arial', 64))
        doc_icon.pack(expand=True)
        
        # Try to read text files
        file_ext = os.path.splitext(file_path)[1].lower()
        if file_ext == '.txt':
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read(1000)  # First 1000 characters
                
                text_widget = tk.Text(preview_frame, wrap=tk.WORD, height=10,
                                     font=('Arial', 10))
                text_widget.pack(fill=tk.BOTH, expand=True, pady=20)
                text_widget.insert(tk.END, content)
                if len(content) == 1000:
                    text_widget.insert(tk.END, "\n\n... (truncated)")
                text_widget.config(state=tk.DISABLED)
                
            except Exception as e:
                info_label = tk.Label(preview_frame, text=f"Cannot preview text: {e}",
                                     bg='white', fg='red', font=('Arial', 12))
                info_label.pack()
        else:
            info_label = tk.Label(preview_frame, text="Document file\nClick 'Open' to view with default application",
                                 bg='white', fg='#667781', font=('Arial', 12), justify=tk.CENTER)
            info_label.pack()
    
    def show_code_preview(self, file_path: str):
        """Show code file preview."""
        preview_frame = tk.Frame(self.preview_window, bg='#1E1E1E')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(2000)  # First 2000 characters
            
            # Code text widget with syntax highlighting colors
            text_widget = tk.Text(preview_frame, wrap=tk.NONE, bg='#1E1E1E', fg='#F8F8F2',
                                 font=('Consolas', 10), insertbackground='white')
            text_widget.pack(fill=tk.BOTH, expand=True)
            
            # Add scrollbars
            v_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=text_widget.yview)
            v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            text_widget.config(yscrollcommand=v_scrollbar.set)
            
            h_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, command=text_widget.xview)
            h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
            text_widget.config(xscrollcommand=h_scrollbar.set)
            
            text_widget.insert(tk.END, content)
            if len(content) == 2000:
                text_widget.insert(tk.END, "\n\n... (truncated)")
            text_widget.config(state=tk.DISABLED)
            
        except Exception as e:
            self.show_error_preview(f"Cannot preview code: {e}")
    
    def show_generic_preview(self, file_path: str, file_ext: str):
        """Show generic file preview."""
        preview_frame = tk.Frame(self.preview_window, bg='#F0F0F0')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Generic file icon
        icon_text = self.get_file_icon(file_ext)
        file_icon = tk.Label(preview_frame, text=icon_text, bg='#F0F0F0', fg='#075E54',
                            font=('Arial', 64))
        file_icon.pack(expand=True)
        
        # File type info
        file_type = self.get_file_type_description(file_ext)
        info_label = tk.Label(preview_frame, text=f"{file_type}\nClick 'Open' to view with default application",
                             bg='#F0F0F0', fg='#667781', font=('Arial', 12), justify=tk.CENTER)
        info_label.pack()
    
    def show_error_preview(self, error_message: str):
        """Show error preview."""
        preview_frame = tk.Frame(self.preview_window, bg='#F0F0F0')
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        error_icon = tk.Label(preview_frame, text="⚠️", bg='#F0F0F0', fg='red',
                             font=('Arial', 64))
        error_icon.pack(expand=True)
        
        error_label = tk.Label(preview_frame, text=error_message, bg='#F0F0F0', fg='red',
                              font=('Arial', 12), justify=tk.CENTER)
        error_label.pack()
    
    def create_action_buttons(self, file_path: str):
        """Create action buttons."""
        button_frame = tk.Frame(self.preview_window, bg='#F0F0F0')
        button_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # Open with default app
        open_btn = tk.Button(button_frame, text="📂 Open", bg='#25D366', fg='white',
                            relief=tk.FLAT, padx=20, pady=10, font=('Arial', 10, 'bold'),
                            command=lambda: self.open_with_default_app(file_path))
        open_btn.pack(side=tk.LEFT, padx=5)
        
        # Show in folder
        folder_btn = tk.Button(button_frame, text="📁 Show in Folder", bg='#075E54', fg='white',
                              relief=tk.FLAT, padx=20, pady=10, font=('Arial', 10, 'bold'),
                              command=lambda: self.show_in_folder(file_path))
        folder_btn.pack(side=tk.LEFT, padx=5)
        
        # Save as
        save_btn = tk.Button(button_frame, text="💾 Save As", bg='#34495E', fg='white',
                            relief=tk.FLAT, padx=20, pady=10, font=('Arial', 10, 'bold'),
                            command=lambda: self.save_file_as(file_path))
        save_btn.pack(side=tk.LEFT, padx=5)
        
        # Close
        close_btn = tk.Button(button_frame, text="❌ Close", bg='#E74C3C', fg='white',
                             relief=tk.FLAT, padx=20, pady=10, font=('Arial', 10, 'bold'),
                             command=self.close_preview)
        close_btn.pack(side=tk.RIGHT, padx=5)
    
    def open_with_default_app(self, file_path: str):
        """Open file with default application."""
        try:
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', file_path])
            else:  # Linux
                subprocess.run(['xdg-open', file_path])
        except Exception as e:
            messagebox.showerror("Error", f"Cannot open file: {e}")
    
    def show_in_folder(self, file_path: str):
        """Show file in folder."""
        try:
            folder_path = os.path.dirname(file_path)
            if platform.system() == 'Windows':
                subprocess.run(['explorer', '/select,', file_path])
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', '-R', file_path])
            else:  # Linux
                subprocess.run(['xdg-open', folder_path])
        except Exception as e:
            messagebox.showerror("Error", f"Cannot show in folder: {e}")
    
    def save_file_as(self, file_path: str):
        """Save file to a new location."""
        from tkinter import filedialog
        
        file_name = os.path.basename(file_path)
        save_path = filedialog.asksaveasfilename(
            title="Save File As",
            initialname=file_name,
            defaultextension=os.path.splitext(file_name)[1]
        )
        
        if save_path:
            try:
                import shutil
                shutil.copy2(file_path, save_path)
                messagebox.showinfo("Success", f"File saved to {save_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Cannot save file: {e}")
    
    def get_file_icon(self, file_ext: str) -> str:
        """Get emoji icon for file type."""
        if file_ext in self.image_types:
            return "🖼️"
        elif file_ext in self.video_types:
            return "🎥"
        elif file_ext in self.audio_types:
            return "🎵"
        elif file_ext in {'.pdf'}:
            return "📕"
        elif file_ext in {'.doc', '.docx'}:
            return "📘"
        elif file_ext in {'.txt'}:
            return "📄"
        elif file_ext in self.code_types:
            return "💻"
        elif file_ext in self.archive_types:
            return "📦"
        else:
            return "📎"
    
    def get_file_type_description(self, file_ext: str) -> str:
        """Get human-readable file type description."""
        type_map = {
            '.jpg': 'JPEG Image', '.jpeg': 'JPEG Image', '.png': 'PNG Image',
            '.gif': 'GIF Image', '.bmp': 'Bitmap Image', '.webp': 'WebP Image',
            '.mp4': 'MP4 Video', '.avi': 'AVI Video', '.mov': 'QuickTime Video',
            '.mkv': 'Matroska Video', '.wmv': 'Windows Media Video',
            '.mp3': 'MP3 Audio', '.wav': 'WAV Audio', '.ogg': 'OGG Audio',
            '.m4a': 'M4A Audio', '.flac': 'FLAC Audio',
            '.pdf': 'PDF Document', '.doc': 'Word Document', '.docx': 'Word Document',
            '.txt': 'Text File', '.rtf': 'Rich Text Format',
            '.py': 'Python Script', '.js': 'JavaScript File', '.html': 'HTML File',
            '.css': 'CSS Stylesheet', '.java': 'Java Source', '.cpp': 'C++ Source',
            '.zip': 'ZIP Archive', '.rar': 'RAR Archive', '.7z': '7-Zip Archive'
        }
        
        return type_map.get(file_ext, f'{file_ext.upper()} File')
    
    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
        
        return f"{size:.1f} {size_names[i]}"
    
    def close_preview(self):
        """Close the preview window."""
        if self.preview_window:
            self.preview_window.destroy()
            self.preview_window = None
