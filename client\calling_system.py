import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
from typing import Dict, Any, Optional, Callable
from datetime import datetime
import cv2
import numpy as np
from PIL import Image, ImageTk
import pyaudio
import wave

class CallManager:
    """Advanced calling system with video and audio calls like WhatsApp."""
    
    def __init__(self, parent, user_info: Dict[str, Any], api_client, ws_client):
        self.parent = parent
        self.user_info = user_info
        self.api_client = api_client
        self.ws_client = ws_client
        
        # Call state
        self.current_call = None
        self.call_window = None
        self.is_calling = False
        self.is_in_call = False
        self.call_type = None  # 'audio' or 'video'
        
        # Video components
        self.camera = None
        self.video_frame = None
        self.video_label = None
        self.remote_video_label = None
        
        # Audio components
        self.audio = None
        self.audio_stream = None
        
        # Call timer
        self.call_start_time = None
        self.call_timer_id = None
        
    def start_voice_call(self, contact_user: Dict[str, Any]):
        """Start a voice call with a contact."""
        self.call_type = 'audio'
        self.current_call = {
            'type': 'outgoing',
            'contact': contact_user,
            'call_type': 'audio'
        }
        
        self.show_calling_interface()
        self.send_call_invitation(contact_user['id'], 'audio')
    
    def start_video_call(self, contact_user: Dict[str, Any]):
        """Start a video call with a contact."""
        self.call_type = 'video'
        self.current_call = {
            'type': 'outgoing',
            'contact': contact_user,
            'call_type': 'video'
        }
        
        self.show_calling_interface()
        self.send_call_invitation(contact_user['id'], 'video')
    
    def show_calling_interface(self):
        """Show the calling interface."""
        if self.call_window:
            return
        
        self.call_window = tk.Toplevel(self.parent)
        self.call_window.title("WhatsApp Call")
        
        if self.call_type == 'video':
            self.call_window.geometry("800x600")
            self.setup_video_call_ui()
        else:
            self.call_window.geometry("400x500")
            self.setup_audio_call_ui()
        
        self.call_window.configure(bg='#075E54')
        self.call_window.resizable(False, False)
        self.call_window.transient(self.parent)
        self.call_window.protocol("WM_DELETE_WINDOW", self.end_call)
        
        # Center window
        self.center_window()
    
    def center_window(self):
        """Center the call window on screen."""
        self.call_window.update_idletasks()
        width = self.call_window.winfo_width()
        height = self.call_window.winfo_height()
        x = (self.call_window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.call_window.winfo_screenheight() // 2) - (height // 2)
        self.call_window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_video_call_ui(self):
        """Set up video call interface."""
        # Main video area
        video_frame = tk.Frame(self.call_window, bg='black')
        video_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Remote video (main area)
        self.remote_video_label = tk.Label(video_frame, bg='black', text="Connecting...", 
                                          fg='white', font=('Arial', 16))
        self.remote_video_label.pack(fill=tk.BOTH, expand=True)
        
        # Local video (small overlay)
        local_video_frame = tk.Frame(video_frame, bg='gray', width=150, height=100)
        local_video_frame.place(relx=0.02, rely=0.02)
        local_video_frame.pack_propagate(False)
        
        self.video_label = tk.Label(local_video_frame, bg='gray', text="You", 
                                   fg='white', font=('Arial', 10))
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # Call info overlay
        info_frame = tk.Frame(video_frame, bg='#075E54')
        info_frame.place(relx=0.5, rely=0.05, anchor='n')
        
        contact_name = self.current_call['contact']['username']
        self.call_status_label = tk.Label(info_frame, text=f"Calling {contact_name}...", 
                                         bg='#075E54', fg='white', font=('Arial', 14, 'bold'))
        self.call_status_label.pack(padx=20, pady=10)
        
        self.call_timer_label = tk.Label(info_frame, text="00:00", 
                                        bg='#075E54', fg='white', font=('Arial', 12))
        self.call_timer_label.pack(padx=20)
        
        # Control buttons
        self.setup_call_controls()
        
        # Start camera
        self.start_camera()
    
    def setup_audio_call_ui(self):
        """Set up audio call interface."""
        # Contact info
        info_frame = tk.Frame(self.call_window, bg='#075E54')
        info_frame.pack(fill=tk.X, pady=20)
        
        # Contact avatar (placeholder)
        avatar_frame = tk.Frame(info_frame, bg='#25D366', width=120, height=120)
        avatar_frame.pack(pady=20)
        avatar_frame.pack_propagate(False)
        
        contact_name = self.current_call['contact']['username']
        avatar_label = tk.Label(avatar_frame, text=contact_name[0].upper(), 
                               bg='#25D366', fg='white', font=('Arial', 48, 'bold'))
        avatar_label.pack(expand=True)
        
        # Contact name
        name_label = tk.Label(info_frame, text=contact_name, 
                             bg='#075E54', fg='white', font=('Arial', 18, 'bold'))
        name_label.pack(pady=10)
        
        # Call status
        self.call_status_label = tk.Label(info_frame, text="Calling...", 
                                         bg='#075E54', fg='white', font=('Arial', 14))
        self.call_status_label.pack()
        
        # Call timer
        self.call_timer_label = tk.Label(info_frame, text="00:00", 
                                        bg='#075E54', fg='white', font=('Arial', 16, 'bold'))
        self.call_timer_label.pack(pady=10)
        
        # Audio visualization (placeholder)
        audio_frame = tk.Frame(self.call_window, bg='#075E54', height=100)
        audio_frame.pack(fill=tk.X, padx=20, pady=20)
        
        audio_canvas = tk.Canvas(audio_frame, bg='#0D7377', height=80, highlightthickness=0)
        audio_canvas.pack(fill=tk.X, pady=10)
        
        # Animate audio bars
        self.animate_audio_bars(audio_canvas)
        
        # Control buttons
        self.setup_call_controls()
    
    def setup_call_controls(self):
        """Set up call control buttons."""
        controls_frame = tk.Frame(self.call_window, bg='#075E54')
        controls_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=20)
        
        button_frame = tk.Frame(controls_frame, bg='#075E54')
        button_frame.pack()
        
        # Mute button
        self.mute_button = tk.Button(button_frame, text="🔇", font=('Arial', 20),
                                    bg='#34495E', fg='white', relief=tk.FLAT,
                                    width=3, height=2, command=self.toggle_mute)
        self.mute_button.pack(side=tk.LEFT, padx=10)
        
        # Speaker button (audio calls)
        if self.call_type == 'audio':
            self.speaker_button = tk.Button(button_frame, text="🔊", font=('Arial', 20),
                                           bg='#34495E', fg='white', relief=tk.FLAT,
                                           width=3, height=2, command=self.toggle_speaker)
            self.speaker_button.pack(side=tk.LEFT, padx=10)
        
        # Camera button (video calls)
        if self.call_type == 'video':
            self.camera_button = tk.Button(button_frame, text="📹", font=('Arial', 20),
                                          bg='#34495E', fg='white', relief=tk.FLAT,
                                          width=3, height=2, command=self.toggle_camera)
            self.camera_button.pack(side=tk.LEFT, padx=10)
        
        # End call button
        self.end_call_button = tk.Button(button_frame, text="📞", font=('Arial', 20),
                                        bg='#E74C3C', fg='white', relief=tk.FLAT,
                                        width=3, height=2, command=self.end_call)
        self.end_call_button.pack(side=tk.LEFT, padx=10)
    
    def start_camera(self):
        """Start camera for video calls."""
        if self.call_type != 'video':
            return
        
        try:
            self.camera = cv2.VideoCapture(0)
            if self.camera.isOpened():
                self.update_video_feed()
            else:
                self.video_label.config(text="Camera\nNot Available", fg='red')
        except Exception as e:
            print(f"Camera error: {e}")
            self.video_label.config(text="Camera\nError", fg='red')
    
    def update_video_feed(self):
        """Update video feed from camera."""
        if not self.camera or not self.camera.isOpened():
            return
        
        try:
            ret, frame = self.camera.read()
            if ret:
                # Resize frame for local video
                frame = cv2.resize(frame, (150, 100))
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
                # Convert to PhotoImage
                image = Image.fromarray(frame)
                photo = ImageTk.PhotoImage(image)
                
                # Update label
                if self.video_label:
                    self.video_label.config(image=photo, text="")
                    self.video_label.image = photo  # Keep reference
            
            # Schedule next update
            if self.call_window and self.is_in_call:
                self.call_window.after(30, self.update_video_feed)
                
        except Exception as e:
            print(f"Video update error: {e}")
    
    def animate_audio_bars(self, canvas):
        """Animate audio visualization bars."""
        if not canvas.winfo_exists():
            return
        
        canvas.delete("all")
        width = canvas.winfo_width()
        height = canvas.winfo_height()
        
        if width > 1:
            # Create animated bars
            import random
            bar_count = 20
            bar_width = width // bar_count
            
            for i in range(bar_count):
                bar_height = random.randint(10, height - 10)
                x1 = i * bar_width
                y1 = (height - bar_height) // 2
                x2 = x1 + bar_width - 2
                y2 = y1 + bar_height
                
                color = f"#{random.randint(100, 255):02x}{random.randint(200, 255):02x}00"
                canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline=color)
        
        # Schedule next animation
        if self.call_window and self.is_calling:
            self.call_window.after(200, lambda: self.animate_audio_bars(canvas))
    
    def send_call_invitation(self, user_id: int, call_type: str):
        """Send call invitation via WebSocket."""
        if self.ws_client:
            message = {
                "type": "call_invitation",
                "data": {
                    "caller_id": self.user_info['id'],
                    "caller_name": self.user_info['username'],
                    "target_user_id": user_id,
                    "call_type": call_type
                }
            }
            self.ws_client.send_message(message)
        
        self.is_calling = True
        
        # Simulate call timeout after 30 seconds
        self.call_window.after(30000, self.call_timeout)
    
    def call_timeout(self):
        """Handle call timeout."""
        if self.is_calling and not self.is_in_call:
            self.call_status_label.config(text="No answer")
            self.call_window.after(2000, self.end_call)
    
    def accept_call(self):
        """Accept incoming call."""
        self.is_calling = False
        self.is_in_call = True
        self.call_start_time = time.time()
        
        self.call_status_label.config(text="Connected")
        self.start_call_timer()
        
        # Start audio/video streams
        if self.call_type == 'video':
            self.start_camera()
    
    def start_call_timer(self):
        """Start call duration timer."""
        if self.is_in_call and self.call_start_time:
            elapsed = time.time() - self.call_start_time
            minutes = int(elapsed // 60)
            seconds = int(elapsed % 60)
            
            self.call_timer_label.config(text=f"{minutes:02d}:{seconds:02d}")
            
            # Schedule next update
            self.call_timer_id = self.call_window.after(1000, self.start_call_timer)
    
    def toggle_mute(self):
        """Toggle microphone mute."""
        # Implementation would control microphone
        current_text = self.mute_button.cget('text')
        if current_text == "🔇":
            self.mute_button.config(text="🎤", bg='#E74C3C')
        else:
            self.mute_button.config(text="🔇", bg='#34495E')
    
    def toggle_speaker(self):
        """Toggle speaker on/off."""
        current_text = self.speaker_button.cget('text')
        if current_text == "🔊":
            self.speaker_button.config(text="🔇", bg='#E74C3C')
        else:
            self.speaker_button.config(text="🔊", bg='#34495E')
    
    def toggle_camera(self):
        """Toggle camera on/off."""
        current_text = self.camera_button.cget('text')
        if current_text == "📹":
            self.camera_button.config(text="📷", bg='#E74C3C')
            # Stop camera
            if self.camera:
                self.camera.release()
                self.camera = None
            self.video_label.config(image="", text="Camera Off", fg='white')
        else:
            self.camera_button.config(text="📹", bg='#34495E')
            # Start camera
            self.start_camera()
    
    def end_call(self):
        """End the current call."""
        self.is_calling = False
        self.is_in_call = False
        
        # Stop timer
        if self.call_timer_id:
            self.call_window.after_cancel(self.call_timer_id)
        
        # Release camera
        if self.camera:
            self.camera.release()
            self.camera = None
        
        # Close call window
        if self.call_window:
            self.call_window.destroy()
            self.call_window = None
        
        # Send end call message
        if self.ws_client and self.current_call:
            message = {
                "type": "call_ended",
                "data": {
                    "caller_id": self.user_info['id'],
                    "target_user_id": self.current_call['contact']['id']
                }
            }
            self.ws_client.send_message(message)
        
        self.current_call = None
    
    def handle_incoming_call(self, call_data: Dict[str, Any]):
        """Handle incoming call invitation."""
        caller_name = call_data.get('caller_name', 'Unknown')
        call_type = call_data.get('call_type', 'audio')
        
        # Show incoming call dialog
        response = messagebox.askyesno(
            "Incoming Call",
            f"{caller_name} is calling you.\nCall type: {call_type.title()}\n\nAccept call?",
            icon='question'
        )
        
        if response:
            # Accept call
            self.current_call = {
                'type': 'incoming',
                'contact': {'id': call_data['caller_id'], 'username': caller_name},
                'call_type': call_type
            }
            self.call_type = call_type
            self.show_calling_interface()
            self.accept_call()
        else:
            # Decline call
            if self.ws_client:
                message = {
                    "type": "call_declined",
                    "data": {
                        "caller_id": call_data['caller_id'],
                        "target_user_id": self.user_info['id']
                    }
                }
                self.ws_client.send_message(message)
    
    def cleanup(self):
        """Clean up calling resources."""
        if self.is_calling or self.is_in_call:
            self.end_call()
        
        if self.camera:
            self.camera.release()
        
        if self.audio:
            self.audio.terminate()
