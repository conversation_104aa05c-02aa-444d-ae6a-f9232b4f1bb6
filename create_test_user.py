#!/usr/bin/env python3
"""
Create a test user with known credentials.
"""

import requests
import json

def create_test_user():
    """Create a test user."""
    base_url = "http://127.0.0.1:8000"
    
    print("🧪 Creating Test User")
    print("=" * 30)
    
    # Test user data
    import time
    timestamp = int(time.time())
    user_data = {
        "username": f"testuser{timestamp}",
        "email": f"test{timestamp}@example.com",
        "password": "testpass123"
    }
    
    print(f"Creating user: {user_data['username']}")
    
    try:
        # Register user
        response = requests.post(f"{base_url}/auth/register", json=user_data)
        if response.status_code == 200:
            print("✅ User created successfully!")
            user_response = response.json()
            print(f"   User ID: {user_response['id']}")
            print(f"   Username: {user_response['username']}")
        else:
            print(f"❌ User creation failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ User creation error: {e}")
        return False
    
    # Test login
    print("\nTesting login...")
    login_data = {
        "username": user_data["username"],
        "password": user_data["password"]
    }
    
    try:
        response = requests.post(f"{base_url}/auth/login", json=login_data)
        if response.status_code == 200:
            print("✅ Login successful!")
            token_data = response.json()
            print(f"   Access token: {token_data['access_token'][:50]}...")
            return True
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False

if __name__ == "__main__":
    success = create_test_user()
    if success:
        print("\n🎉 Test user created and login works!")
        print("\n📱 You can now use these credentials:")
        print("   Username: testuser")
        print("   Password: testpass123")
    else:
        print("\n❌ Failed to create test user!")
