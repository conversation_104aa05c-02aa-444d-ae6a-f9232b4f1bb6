from sqlalchemy import Column, Inte<PERSON>, String, DateTime, <PERSON>olean, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from enum import Enum

# Message Types Enum
class MessageType(str, Enum):
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    VOICE = "voice"
    VIDEO = "video"
    DOCUMENT = "document"
    LOCATION = "location"
    CONTACT = "contact"
    STICKER = "sticker"
    GIF = "gif"
    SYSTEM = "system"

# Message Status Enum
class MessageStatus(str, Enum):
    SENDING = "sending"
    SENT = "sent"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"

# SQLAlchemy Message Model
class Message(Base):
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    chat_room_id = Column(Integer, ForeignKey("chat_rooms.id"), nullable=False)
    sender_id = Column(Integer, Foreign<PERSON>ey("users.id"), nullable=False)
    content = Column(Text, nullable=False)
    message_type = Column(String(20), default=MessageType.TEXT)

    # File/Media fields
    file_url = Column(String(255), nullable=True)
    file_name = Column(String(255), nullable=True)
    file_size = Column(Integer, nullable=True)
    thumbnail_url = Column(String(255), nullable=True)  # For image/video thumbnails
    duration = Column(Integer, nullable=True)  # For voice/video messages (seconds)

    # Message status and metadata
    status = Column(String(20), default=MessageStatus.SENT)
    is_edited = Column(Boolean, default=False)
    edited_at = Column(DateTime(timezone=True), nullable=True)
    is_deleted = Column(Boolean, default=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # Reply functionality
    reply_to_id = Column(Integer, ForeignKey("messages.id"), nullable=True)

    # Forward functionality
    forwarded_from_id = Column(Integer, ForeignKey("messages.id"), nullable=True)
    forward_count = Column(Integer, default=0)

    # Location data (for location messages)
    latitude = Column(String(50), nullable=True)
    longitude = Column(String(50), nullable=True)
    location_name = Column(String(255), nullable=True)

    # Contact data (for contact messages)
    contact_name = Column(String(255), nullable=True)
    contact_phone = Column(String(50), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    read_receipts = relationship("MessageReadReceipt", back_populates="message")
    reactions = relationship("MessageReaction", back_populates="message")

    # Self-referential relationships for replies and forwards
    reply_to = relationship("Message", remote_side=[id], foreign_keys=[reply_to_id])
    forwarded_from = relationship("Message", remote_side=[id], foreign_keys=[forwarded_from_id])

# Message Reactions
class MessageReaction(Base):
    __tablename__ = "message_reactions"

    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey("messages.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    emoji = Column(String(10), nullable=False)  # Unicode emoji
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    message = relationship("Message", back_populates="reactions")
    user = relationship("User")

# Enhanced Message Read Receipts
class MessageReadReceipt(Base):
    __tablename__ = "message_read_receipts"

    id = Column(Integer, primary_key=True, index=True)
    message_id = Column(Integer, ForeignKey("messages.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    status = Column(String(20), default=MessageStatus.DELIVERED)  # delivered, read
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    read_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    message = relationship("Message", back_populates="read_receipts")
    user = relationship("User")

# Typing Indicators
class TypingIndicator(Base):
    __tablename__ = "typing_indicators"
    
    id = Column(Integer, primary_key=True, index=True)
    chat_room_id = Column(Integer, ForeignKey("chat_rooms.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False)

# Pydantic Models for API
class MessageBase(BaseModel):
    content: str
    message_type: MessageType = MessageType.TEXT

class MessageCreate(MessageBase):
    reply_to_id: Optional[int] = None
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    location_name: Optional[str] = None
    contact_name: Optional[str] = None
    contact_phone: Optional[str] = None

class MessageUpdate(BaseModel):
    content: str

class MessageReply(BaseModel):
    message_id: int
    content: str

class MessageForward(BaseModel):
    message_ids: List[int]
    target_chat_room_id: int

class MessageReactionCreate(BaseModel):
    message_id: int
    emoji: str

class MessageReactionResponse(BaseModel):
    id: int
    message_id: int
    user_id: int
    username: str
    emoji: str
    created_at: datetime

    class Config:
        from_attributes = True

class MessageResponse(MessageBase):
    id: int
    chat_room_id: int
    sender_id: int
    sender_username: Optional[str] = None

    # File/Media fields
    file_url: Optional[str] = None
    file_name: Optional[str] = None
    file_size: Optional[int] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[int] = None

    # Status and metadata
    status: MessageStatus
    is_edited: bool
    edited_at: Optional[datetime] = None
    is_deleted: bool

    # Reply and forward info
    reply_to_id: Optional[int] = None
    reply_to_message: Optional[str] = None  # Content of replied message
    forwarded_from_id: Optional[int] = None
    forward_count: int

    # Location data
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    location_name: Optional[str] = None

    # Contact data
    contact_name: Optional[str] = None
    contact_phone: Optional[str] = None

    # Timestamps and interactions
    created_at: datetime
    reactions: Optional[List[MessageReactionResponse]] = []
    read_by: Optional[List[int]] = []  # List of user IDs who read the message

    class Config:
        from_attributes = True

class MessageWithSender(MessageResponse):
    sender_username: str
    sender_avatar: Optional[str] = None

class TypingIndicatorResponse(BaseModel):
    chat_room_id: int
    user_id: int
    username: str
    started_at: datetime
    expires_at: datetime
    
    class Config:
        from_attributes = True

class FileUploadResponse(BaseModel):
    file_url: str
    file_name: str
    file_size: int
    message_type: MessageType

# WebSocket Message Types
class WSMessageType(str, Enum):
    MESSAGE = "message"
    TYPING_START = "typing_start"
    TYPING_STOP = "typing_stop"
    USER_JOINED = "user_joined"
    USER_LEFT = "user_left"
    USER_ONLINE = "user_online"
    USER_OFFLINE = "user_offline"
    MESSAGE_READ = "message_read"
    ERROR = "error"

class WSMessage(BaseModel):
    type: WSMessageType
    data: dict
    chat_room_id: Optional[int] = None
    timestamp: datetime = datetime.utcnow()

class WSTypingMessage(BaseModel):
    type: WSMessageType
    chat_room_id: int
    user_id: int
    username: str
    timestamp: datetime = datetime.utcnow()

class WSPresenceMessage(BaseModel):
    type: WSMessageType
    user_id: int
    username: str
    is_online: bool
    timestamp: datetime = datetime.utcnow()

class WSErrorMessage(BaseModel):
    type: WSMessageType = WSMessageType.ERROR
    error: str
    timestamp: datetime = datetime.utcnow()
