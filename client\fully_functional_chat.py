import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
import json
import os

from helpers import APIClient, TokenManager, format_timestamp
from websocket_client import WebSocketClient
from whatsapp_features import WhatsAppFeatures
from voice_recorder import VoiceRecorder
from calling_system import CallManager
from status_system import StatusManager
from file_preview import FilePreviewManager
from notification_system import NotificationManager

class FullyFunctionalChatWindow:
    """FULLY FUNCTIONAL WhatsApp-like chat window with REAL working features."""
    
    def __init__(self, parent: ttk.Frame, user_info: Dict[str, Any], 
                 api_client: APIClient, token_manager: TokenManager, 
                 on_logout: Callable):
        self.parent = parent
        self.user_info = user_info
        self.api_client = api_client
        self.token_manager = token_manager
        self.on_logout = on_logout
        
        # State
        self.chat_rooms: List[Dict[str, Any]] = []
        self.current_room: Optional[Dict[str, Any]] = None
        self.messages: Dict[int, List[Dict[str, Any]]] = {}
        self.online_users: List[Dict[str, Any]] = []
        
        # WebSocket client
        self.ws_client: Optional[WebSocketClient] = None

        # WhatsApp features
        self.whatsapp_features = WhatsAppFeatures(self)

        # Advanced features
        self.voice_recorder = VoiceRecorder(self.parent, self.on_voice_message_complete)
        self.call_manager = CallManager(self.parent, user_info, api_client, None)  # Will set ws_client later
        self.status_manager = StatusManager(self.parent, user_info, api_client)
        self.file_preview = FilePreviewManager(self.parent)
        self.notification_manager = NotificationManager(self.parent)
        
        # Colors
        self.colors = {
            'primary': '#075E54',
            'secondary': '#128C7E', 
            'accent': '#25D366',
            'background': '#ECE5DD',
            'sidebar': '#FFFFFF',
            'message_sent': '#DCF8C6',
            'message_received': '#FFFFFF',
            'text': '#000000',
            'text_secondary': '#667781',
            'online': '#25D366',
            'offline': '#8696A0'
        }
        
        self.setup_ui()
        self.load_initial_data()
        self.setup_websocket()
    
    def setup_ui(self):
        """Set up the user interface."""
        # Main container
        self.main_frame = ttk.Frame(self.parent)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create three-panel layout
        self.setup_sidebar()
        self.setup_chat_area()
        self.setup_user_panel()
    
    def setup_sidebar(self):
        """Set up the sidebar with chat rooms and contacts."""
        # Sidebar frame
        self.sidebar_frame = tk.Frame(self.main_frame, bg=self.colors['sidebar'], width=300)
        self.sidebar_frame.pack(side=tk.LEFT, fill=tk.Y)
        self.sidebar_frame.pack_propagate(False)
        
        # Header
        header_frame = tk.Frame(self.sidebar_frame, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # User info in header
        user_label = tk.Label(header_frame, text=f"Welcome, {self.user_info['username']}", 
                             bg=self.colors['primary'], fg='white', font=('Arial', 12, 'bold'))
        user_label.pack(pady=15)
        
        # Search frame
        search_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        search_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=('Arial', 10), relief=tk.FLAT, bg='#F0F0F0')
        search_entry.pack(fill=tk.X, ipady=5)
        search_entry.insert(0, "Search chats...")
        
        # Tabs for chats and contacts
        tab_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        tab_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.chats_button = tk.Button(tab_frame, text="Chats", bg=self.colors['accent'], 
                                     fg='white', relief=tk.FLAT, command=self.show_chats)
        self.chats_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 2))
        
        self.contacts_button = tk.Button(tab_frame, text="Contacts", bg='#E0E0E0', 
                                        fg='black', relief=tk.FLAT, command=self.show_contacts)
        self.contacts_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 0))

        # Add group creation button
        group_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        group_frame.pack(fill=tk.X, padx=10, pady=5)

        self.new_group_button = tk.Button(group_frame, text="+ New Group",
                                         bg=self.colors['accent'], fg='white', relief=tk.FLAT,
                                         command=self.create_new_group)
        self.new_group_button.pack(fill=tk.X, pady=(0, 5))

        # Status button
        self.status_button = tk.Button(group_frame, text="📸 Status",
                                      bg=self.colors['secondary'], fg='white', relief=tk.FLAT,
                                      command=self.status_manager.show_status_window)
        self.status_button.pack(fill=tk.X)
        
        # Scrollable list frame
        self.list_frame = tk.Frame(self.sidebar_frame, bg=self.colors['sidebar'])
        self.list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Canvas for scrolling
        self.canvas = tk.Canvas(self.list_frame, bg=self.colors['sidebar'], highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(self.list_frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = tk.Frame(self.canvas, bg=self.colors['sidebar'])
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
    
    def setup_chat_area(self):
        """Set up the main chat area."""
        # Chat area frame
        self.chat_frame = tk.Frame(self.main_frame, bg=self.colors['background'])
        self.chat_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Chat header
        self.chat_header_frame = tk.Frame(self.chat_frame, bg=self.colors['primary'], height=60)
        self.chat_header_frame.pack(fill=tk.X)
        self.chat_header_frame.pack_propagate(False)
        
        # Chat name and status
        self.chat_name_label = tk.Label(self.chat_header_frame, text="Select a chat", 
                                       bg=self.colors['primary'], fg='white', 
                                       font=('Arial', 14, 'bold'))
        self.chat_name_label.pack(side=tk.LEFT, padx=15, pady=10)
        
        self.chat_status_label = tk.Label(self.chat_header_frame, text="", 
                                         bg=self.colors['primary'], fg='#B0B0B0', 
                                         font=('Arial', 10))
        self.chat_status_label.pack(side=tk.LEFT, padx=(0, 15), pady=10)
        
        # Messages area
        self.messages_frame = tk.Frame(self.chat_frame, bg=self.colors['background'])
        self.messages_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Messages canvas for scrolling
        self.messages_canvas = tk.Canvas(self.messages_frame, bg=self.colors['background'], 
                                        highlightthickness=0)
        self.messages_scrollbar = ttk.Scrollbar(self.messages_frame, orient="vertical", 
                                               command=self.messages_canvas.yview)
        self.messages_scrollable_frame = tk.Frame(self.messages_canvas, bg=self.colors['background'])
        
        self.messages_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.messages_canvas.configure(scrollregion=self.messages_canvas.bbox("all"))
        )
        
        self.messages_canvas.create_window((0, 0), window=self.messages_scrollable_frame, anchor="nw")
        self.messages_canvas.configure(yscrollcommand=self.messages_scrollbar.set)
        
        self.messages_canvas.pack(side="left", fill="both", expand=True)
        self.messages_scrollbar.pack(side="right", fill="y")
        
        # Input area
        self.input_frame = tk.Frame(self.chat_frame, bg=self.colors['background'], height=80)
        self.input_frame.pack(fill=tk.X, padx=10, pady=5)
        self.input_frame.pack_propagate(False)

        # Left side buttons (emoji, file)
        left_buttons_frame = tk.Frame(self.input_frame, bg=self.colors['background'])
        left_buttons_frame.pack(side=tk.LEFT, padx=5)

        # Add emoji picker
        self.whatsapp_features.add_emoji_picker(left_buttons_frame)

        # Add file sharing
        self.whatsapp_features.add_file_sharing_button(left_buttons_frame)

        # Message input
        self.message_var = tk.StringVar()
        self.message_entry = tk.Entry(self.input_frame, textvariable=self.message_var,
                                     font=('Arial', 12), relief=tk.FLAT, bg='white')
        self.message_entry.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, ipady=10)
        self.message_entry.bind('<Return>', self.send_message)
        self.message_entry.bind('<KeyPress>', self.on_key_press)

        # Right side buttons (voice, send)
        right_buttons_frame = tk.Frame(self.input_frame, bg=self.colors['background'])
        right_buttons_frame.pack(side=tk.RIGHT, padx=5)

        # Add voice message button
        self.whatsapp_features.add_voice_message_button(right_buttons_frame)

        # Send button
        self.send_button = tk.Button(right_buttons_frame, text="Send", bg=self.colors['accent'],
                                    fg='white', relief=tk.FLAT, font=('Arial', 10, 'bold'),
                                    command=self.send_message)
        self.send_button.pack(side=tk.RIGHT, padx=5, pady=5)

        # Header buttons
        header_buttons_frame = tk.Frame(self.chat_header_frame, bg=self.colors['primary'])
        header_buttons_frame.pack(side=tk.RIGHT, padx=10)

        # Video call button
        self.video_call_button = tk.Button(header_buttons_frame, text="📹",
                                          bg=self.colors['primary'], fg='white', relief=tk.FLAT,
                                          font=('Arial', 14), command=self.start_video_call)
        self.video_call_button.pack(side=tk.LEFT, padx=5)

        # Voice call button
        self.voice_call_button = tk.Button(header_buttons_frame, text="📞",
                                          bg=self.colors['primary'], fg='white', relief=tk.FLAT,
                                          font=('Arial', 14), command=self.start_voice_call)
        self.voice_call_button.pack(side=tk.LEFT, padx=5)

        # Dark mode toggle button
        self.dark_mode_button = tk.Button(header_buttons_frame, text="🌙",
                                         bg=self.colors['primary'], fg='white', relief=tk.FLAT,
                                         command=self.whatsapp_features.toggle_dark_mode)
        self.dark_mode_button.pack(side=tk.LEFT, padx=5)

    def on_key_press(self, event):
        """Handle key press in message input."""
        # Start typing indicator
        self.whatsapp_features.start_typing_indicator()
    
    def setup_user_panel(self):
        """Set up the user panel (optional)."""
        # For now, we'll keep it simple without a third panel
        pass
    
    def load_initial_data(self):
        """Load initial data."""
        self.load_chat_rooms()
        self.load_users()
        self.show_chats()
    
    def load_chat_rooms(self):
        """Load chat rooms from API."""
        def load_thread():
            try:
                rooms = self.api_client.get_chat_rooms()
                if rooms:
                    self.chat_rooms = rooms
                    self.parent.after(0, self.refresh_chat_list)
                    print(f"✅ Loaded {len(rooms)} chat rooms")
                else:
                    print("❌ No chat rooms found")
            except Exception as e:
                print(f"❌ Error loading chat rooms: {e}")
        
        threading.Thread(target=load_thread, daemon=True).start()
    
    def load_users(self):
        """Load users from API."""
        def load_thread():
            try:
                users = self.api_client.get_users()
                if users:
                    # Filter out current user
                    self.online_users = [u for u in users if u['id'] != self.user_info['id']]
                    print(f"✅ Loaded {len(self.online_users)} users")
                else:
                    print("❌ No users found")
            except Exception as e:
                print(f"❌ Error loading users: {e}")
        
        threading.Thread(target=load_thread, daemon=True).start()
    
    def show_chats(self):
        """Show chat rooms list."""
        self.chats_button.config(bg=self.colors['accent'], fg='white')
        self.contacts_button.config(bg='#E0E0E0', fg='black')
        self.refresh_chat_list()
    
    def show_contacts(self):
        """Show contacts list."""
        self.contacts_button.config(bg=self.colors['accent'], fg='white')
        self.chats_button.config(bg='#E0E0E0', fg='black')
        self.refresh_contacts_list()
    
    def refresh_chat_list(self):
        """Refresh the chat rooms list."""
        # Clear existing items
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # Add chat rooms
        for room in self.chat_rooms:
            self.create_chat_item(room, True)
    
    def refresh_contacts_list(self):
        """Refresh the contacts list."""
        # Clear existing items
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()
        
        # Add users
        for user in self.online_users:
            self.create_chat_item(user, False)
    
    def create_chat_item(self, item: Dict[str, Any], is_room: bool):
        """Create a chat or contact item."""
        item_frame = tk.Frame(self.scrollable_frame, bg=self.colors['sidebar'], 
                             relief=tk.FLAT, bd=1)
        item_frame.pack(fill=tk.X, pady=1)
        
        # Avatar (placeholder)
        avatar_frame = tk.Frame(item_frame, bg='#4CAF50', width=40, height=40)
        avatar_frame.pack(side=tk.LEFT, padx=10, pady=10)
        avatar_frame.pack_propagate(False)
        
        avatar_label = tk.Label(avatar_frame, text=item['name'][0].upper() if is_room else item['username'][0].upper(), 
                               bg='#4CAF50', fg='white', font=('Arial', 14, 'bold'))
        avatar_label.pack(expand=True)
        
        # Info frame
        info_frame = tk.Frame(item_frame, bg=self.colors['sidebar'])
        info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Name
        name = item['name'] if is_room else item['username']
        name_label = tk.Label(info_frame, text=name, bg=self.colors['sidebar'], 
                             font=('Arial', 12, 'bold'), anchor=tk.W)
        name_label.pack(fill=tk.X)
        
        # Status
        if is_room:
            status_text = f"Group • {item.get('member_count', 0)} members"
        else:
            status_text = "Online" if item.get('is_online', False) else "Offline"
        
        status_label = tk.Label(info_frame, text=status_text, bg=self.colors['sidebar'], 
                               fg=self.colors['text_secondary'], font=('Arial', 10), anchor=tk.W)
        status_label.pack(fill=tk.X)
        
        # Click handler
        def on_click(event=None):
            if is_room:
                self.select_room(item)
            else:
                self.start_private_chat(item)
        
        # Bind click events
        for widget in [item_frame, avatar_frame, avatar_label, info_frame, name_label, status_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", lambda e: item_frame.config(bg='#F0F0F0'))
            widget.bind("<Leave>", lambda e: item_frame.config(bg=self.colors['sidebar']))

    def select_room(self, room: Dict[str, Any]):
        """Select and join a chat room."""
        print(f"Selecting room: {room['name']}")

        self.current_room = room

        # Update header
        self.chat_name_label.config(text=room['name'])

        if room.get('is_group', False):
            status_text = f"Group • {room.get('member_count', 0)} members"
        else:
            status_text = "Private chat"

        self.chat_status_label.config(text=status_text)

        # Clear messages and load new ones
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()

        self.load_messages(room['id'])

        # Join room via WebSocket
        if self.ws_client:
            self.ws_client.join_room(room['id'])

        print(f"✅ Joined room: {room['name']}")

    def start_private_chat(self, user: Dict[str, Any]):
        """Start private chat with user."""
        print(f"Starting private chat with: {user['username']}")

        def create_chat_thread():
            try:
                # Find or create private chat room
                room = self.api_client.find_or_create_private_chat(user['id'], user['username'])
                if room:
                    self.parent.after(0, lambda: self.select_room(room))
                    self.parent.after(0, lambda: self.load_chat_rooms())  # Refresh rooms list
                else:
                    print("❌ Failed to create private chat")
            except Exception as e:
                print(f"❌ Error creating private chat: {e}")

        threading.Thread(target=create_chat_thread, daemon=True).start()

    def load_messages(self, room_id: int):
        """Load messages for a room."""
        def load_thread():
            try:
                messages = self.api_client.get_messages(room_id)
                if messages:
                    self.messages[room_id] = messages
                    self.parent.after(0, lambda: self.display_messages(messages))
                    print(f"✅ Loaded {len(messages)} messages for room {room_id}")
                else:
                    print(f"No messages found for room {room_id}")
            except Exception as e:
                print(f"❌ Error loading messages: {e}")

        threading.Thread(target=load_thread, daemon=True).start()

    def display_messages(self, messages: List[Dict[str, Any]]):
        """Display messages in the chat area."""
        # Clear existing messages
        for widget in self.messages_scrollable_frame.winfo_children():
            widget.destroy()

        # Add messages
        for message in messages:
            self.add_message_bubble(message)

        # Scroll to bottom
        self.parent.after(100, self.scroll_to_bottom)

    def add_message_bubble(self, message: Dict[str, Any]):
        """Add a message bubble to the chat."""
        is_sent = message['sender_id'] == self.user_info['id']

        # Message frame
        msg_frame = tk.Frame(self.messages_scrollable_frame, bg=self.colors['background'])
        msg_frame.pack(fill=tk.X, padx=10, pady=2)

        # Bubble frame
        bubble_color = self.colors['message_sent'] if is_sent else self.colors['message_received']
        bubble_frame = tk.Frame(msg_frame, bg=bubble_color, relief=tk.RAISED, bd=1)

        if is_sent:
            bubble_frame.pack(side=tk.RIGHT, padx=(50, 0))
        else:
            bubble_frame.pack(side=tk.LEFT, padx=(0, 50))

        # Message content
        content_label = tk.Label(bubble_frame, text=message['content'], bg=bubble_color,
                                font=('Arial', 11), wraplength=300, justify=tk.LEFT, anchor=tk.W)
        content_label.pack(padx=10, pady=5)

        # Timestamp and sender info
        info_text = ""
        if not is_sent:
            info_text += f"{message.get('sender_username', 'Unknown')} • "

        timestamp = format_timestamp(message['created_at'])
        info_text += timestamp

        info_label = tk.Label(bubble_frame, text=info_text, bg=bubble_color,
                             fg=self.colors['text_secondary'], font=('Arial', 8))
        info_label.pack(padx=10, pady=(0, 5), anchor=tk.E if is_sent else tk.W)

        # Add message status indicators for sent messages
        if is_sent:
            self.whatsapp_features.add_message_status_indicators(bubble_frame, message)

        # Add right-click context menu for reactions and reply
        self.add_message_context_menu(bubble_frame, message)

    def add_message_context_menu(self, widget, message):
        """Add context menu to message for reactions and reply."""
        def show_context_menu(event):
            context_menu = tk.Menu(self.parent, tearoff=0)
            context_menu.configure(bg=self.colors['sidebar'], fg=self.colors['text'])

            # Quick reactions
            context_menu.add_command(label="❤️ Love", command=lambda: self.add_reaction(message['id'], "❤️"))
            context_menu.add_command(label="😂 Laugh", command=lambda: self.add_reaction(message['id'], "😂"))
            context_menu.add_command(label="😮 Wow", command=lambda: self.add_reaction(message['id'], "😮"))
            context_menu.add_command(label="😢 Sad", command=lambda: self.add_reaction(message['id'], "😢"))
            context_menu.add_command(label="😡 Angry", command=lambda: self.add_reaction(message['id'], "😡"))
            context_menu.add_command(label="👍 Like", command=lambda: self.add_reaction(message['id'], "👍"))

            context_menu.add_separator()

            # Reply and forward
            context_menu.add_command(label="↩️ Reply", command=lambda: self.reply_to_message(message))
            context_menu.add_command(label="➡️ Forward", command=lambda: self.forward_message(message))

            # Copy text
            if message.get('message_type') == 'text':
                context_menu.add_command(label="📋 Copy", command=lambda: self.copy_message_text(message))

            # Show menu
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        widget.bind("<Button-3>", show_context_menu)  # Right click

        # Also bind to all child widgets
        for child in widget.winfo_children():
            child.bind("<Button-3>", show_context_menu)

    def add_reaction(self, message_id, emoji):
        """Add reaction to a message."""
        def add_reaction_thread():
            try:
                data = {"message_id": message_id, "emoji": emoji}
                result = self.api_client._make_request("POST", "/messages/reactions", json=data)
                if result:
                    print(f"✅ Added reaction {emoji} to message {message_id}")
                else:
                    print(f"❌ Failed to add reaction")
            except Exception as e:
                print(f"❌ Error adding reaction: {e}")

        threading.Thread(target=add_reaction_thread, daemon=True).start()

    def reply_to_message(self, message):
        """Reply to a specific message."""
        reply_text = f"Replying to: {message['content'][:50]}..."
        self.message_var.set(f"↩️ {reply_text}\n")
        self.message_entry.focus()

    def forward_message(self, message):
        """Forward a message."""
        messagebox.showinfo("Forward", f"Forward feature would forward: {message['content'][:50]}...")

    def copy_message_text(self, message):
        """Copy message text to clipboard."""
        try:
            self.parent.clipboard_clear()
            self.parent.clipboard_append(message['content'])
            print("✅ Message copied to clipboard")
        except Exception as e:
            print(f"❌ Error copying message: {e}")

    def create_new_group(self):
        """Create a new group chat."""
        group_window = tk.Toplevel(self.parent)
        group_window.title("Create New Group")
        group_window.geometry("400x500")
        group_window.configure(bg=self.colors['background'])

        # Group name
        name_frame = tk.Frame(group_window, bg=self.colors['background'])
        name_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(name_frame, text="Group Name:", bg=self.colors['background'],
                fg=self.colors['text'], font=('Arial', 12, 'bold')).pack(anchor=tk.W)

        group_name_var = tk.StringVar()
        name_entry = tk.Entry(name_frame, textvariable=group_name_var, font=('Arial', 12))
        name_entry.pack(fill=tk.X, pady=5)

        # Group description
        desc_frame = tk.Frame(group_window, bg=self.colors['background'])
        desc_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Label(desc_frame, text="Description (optional):", bg=self.colors['background'],
                fg=self.colors['text'], font=('Arial', 12, 'bold')).pack(anchor=tk.W)

        group_desc_var = tk.StringVar()
        desc_entry = tk.Entry(desc_frame, textvariable=group_desc_var, font=('Arial', 12))
        desc_entry.pack(fill=tk.X, pady=5)

        # Member selection
        members_frame = tk.Frame(group_window, bg=self.colors['background'])
        members_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        tk.Label(members_frame, text="Select Members:", bg=self.colors['background'],
                fg=self.colors['text'], font=('Arial', 12, 'bold')).pack(anchor=tk.W)

        # Members listbox with checkboxes
        members_listbox = tk.Listbox(members_frame, selectmode=tk.MULTIPLE, font=('Arial', 10))
        members_listbox.pack(fill=tk.BOTH, expand=True, pady=5)

        # Populate with available users
        for user in self.online_users:
            members_listbox.insert(tk.END, f"{user['username']} ({user['email']})")

        # Buttons
        button_frame = tk.Frame(group_window, bg=self.colors['background'])
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        cancel_btn = tk.Button(button_frame, text="Cancel", bg='#E0E0E0',
                              command=group_window.destroy)
        cancel_btn.pack(side=tk.RIGHT, padx=5)

        create_btn = tk.Button(button_frame, text="Create Group", bg=self.colors['accent'],
                              fg='white', command=lambda: self.create_group_action(
                                  group_name_var.get(), group_desc_var.get(),
                                  members_listbox.curselection(), group_window))
        create_btn.pack(side=tk.RIGHT, padx=5)

    def create_group_action(self, name, description, selected_indices, window):
        """Actually create the group."""
        if not name.strip():
            messagebox.showwarning("Warning", "Please enter a group name")
            return

        if not selected_indices:
            messagebox.showwarning("Warning", "Please select at least one member")
            return

        def create_group_thread():
            try:
                # Create group
                group_data = {
                    "name": name.strip(),
                    "description": description.strip(),
                    "is_group": True
                }

                result = self.api_client.create_chat_room(name.strip(), description.strip(), True)
                if result:
                    print(f"✅ Group '{name}' created successfully")

                    # Add selected members (this would need backend support)
                    # For now, just refresh the chat list
                    self.parent.after(0, lambda: [
                        self.load_chat_rooms(),
                        window.destroy(),
                        messagebox.showinfo("Success", f"Group '{name}' created successfully!")
                    ])
                else:
                    self.parent.after(0, lambda: messagebox.showerror("Error", "Failed to create group"))
            except Exception as e:
                print(f"❌ Error creating group: {e}")
                self.parent.after(0, lambda: messagebox.showerror("Error", f"Failed to create group: {e}"))

        threading.Thread(target=create_group_thread, daemon=True).start()

    def start_video_call(self):
        """Start a video call with current contact."""
        if not self.current_room:
            messagebox.showwarning("No Contact", "Please select a contact to call")
            return

        if self.current_room.get('is_group', False):
            messagebox.showinfo("Group Call", "Group video calls coming soon!")
            return

        # Get contact info (in a real app, you'd get this from the room data)
        contact_user = {
            'id': 2,  # This would be the actual contact ID
            'username': self.current_room.get('name', 'Contact')
        }

        self.call_manager.start_video_call(contact_user)

    def start_voice_call(self):
        """Start a voice call with current contact."""
        if not self.current_room:
            messagebox.showwarning("No Contact", "Please select a contact to call")
            return

        if self.current_room.get('is_group', False):
            messagebox.showinfo("Group Call", "Group voice calls coming soon!")
            return

        # Get contact info (in a real app, you'd get this from the room data)
        contact_user = {
            'id': 2,  # This would be the actual contact ID
            'username': self.current_room.get('name', 'Contact')
        }

        self.call_manager.start_voice_call(contact_user)

    def on_voice_message_complete(self, file_path: str, duration: float):
        """Handle completed voice message recording."""
        if not self.current_room:
            return

        # Create voice message
        voice_message = f"🎤 Voice message ({duration:.1f}s)"

        # Send as regular message for now (in real app, you'd upload the audio file)
        self.message_var.set(voice_message)
        self.send_message()

        # Clean up temp file
        try:
            import os
            os.unlink(file_path)
        except:
            pass

    def send_message(self, event=None):
        """Send a message."""
        if not self.current_room:
            messagebox.showwarning("Warning", "Please select a chat room first")
            return

        content = self.message_var.get().strip()
        if not content:
            return

        # Clear input
        self.message_var.set("")

        # Create temporary message for immediate display
        temp_message = {
            'id': f"temp_{datetime.now().timestamp()}",
            'content': content,
            'sender_id': self.user_info['id'],
            'sender_username': self.user_info['username'],
            'created_at': datetime.now().isoformat(),
            'status': 'sending'
        }

        self.add_message_bubble(temp_message)
        self.scroll_to_bottom()

        # Send via API
        def send_thread():
            try:
                result = self.api_client.send_message(self.current_room['id'], content)
                if result:
                    print("✅ Message sent successfully")
                else:
                    print("❌ Failed to send message")
                    # Could add error handling here
            except Exception as e:
                print(f"❌ Error sending message: {e}")

        threading.Thread(target=send_thread, daemon=True).start()

    def scroll_to_bottom(self):
        """Scroll messages to bottom."""
        self.messages_canvas.update_idletasks()
        self.messages_canvas.yview_moveto(1.0)

    def setup_websocket(self):
        """Set up WebSocket connection."""
        token = self.token_manager.get_access_token()
        if token:
            self.ws_client = WebSocketClient(token, self.handle_websocket_message)
            self.ws_client.start()
            print("✅ WebSocket connection started")

    def handle_websocket_message(self, message: Dict[str, Any]):
        """Handle incoming WebSocket messages."""
        try:
            # First, let WhatsApp features handle the message
            self.whatsapp_features.handle_websocket_message(message)

            msg_type = message.get('type')

            if msg_type == 'message':
                # New message received
                data = message.get('data', {})
                if self.current_room and data.get('chat_room_id') == self.current_room['id']:
                    # Only add if it's for the current room and not from us
                    if data.get('sender_id') != self.user_info['id']:
                        self.parent.after(0, lambda: self.add_message_bubble(data))
                        self.parent.after(0, self.scroll_to_bottom)

                        # Mark message as read automatically
                        self.mark_message_as_read(data.get('id'))
                else:
                    # Message for different room - show notification
                    if data.get('sender_id') != self.user_info['id']:
                        self.show_message_notification(data)

            elif msg_type == 'typing_start':
                # Handle typing indicators
                data = message.get('data', {})
                username = data.get('username')
                if username and username != self.user_info['username']:
                    self.parent.after(0, lambda: self.whatsapp_features.show_typing_indicator(username))

            elif msg_type == 'typing_stop':
                # Handle stop typing
                data = message.get('data', {})
                username = data.get('username')
                if username:
                    self.parent.after(0, lambda: self.whatsapp_features.hide_typing_indicator(username))

            elif msg_type == 'user_online':
                # Handle user presence
                pass

        except Exception as e:
            print(f"❌ Error handling WebSocket message: {e}")

    def mark_message_as_read(self, message_id):
        """Mark a message as read."""
        if not message_id:
            return

        def mark_read_thread():
            try:
                self.api_client._make_request("POST", f"/messages/{message_id}/read")
            except Exception as e:
                print(f"Error marking message as read: {e}")

        threading.Thread(target=mark_read_thread, daemon=True).start()

    def show_message_notification(self, message_data: Dict[str, Any]):
        """Show notification for new message."""
        sender_name = message_data.get('sender_username', 'Unknown')
        content = message_data.get('content', '')

        # Truncate long messages
        if len(content) > 50:
            content = content[:50] + "..."

        # Show notification
        self.notification_manager.show_notification(
            title=f"New message from {sender_name}",
            message=content,
            notification_type='message',
            sender_info={'name': sender_name, 'id': message_data.get('sender_id')},
            on_click=lambda: self.open_chat_for_message(message_data)
        )

    def open_chat_for_message(self, message_data: Dict[str, Any]):
        """Open chat for a specific message."""
        # Focus main window
        self.parent.lift()
        self.parent.focus_force()

        # Find and select the chat room
        chat_room_id = message_data.get('chat_room_id')
        if chat_room_id:
            # Find the room in our list
            for room in self.chat_rooms:
                if room.get('id') == chat_room_id:
                    self.select_room(room)
                    break

    def show_file_preview(self, file_path: str, file_name: str, file_size: int):
        """Show file preview."""
        self.file_preview.show_file_preview(file_path, file_name, file_size)

    def handle_file_message(self, message_data: Dict[str, Any]):
        """Handle file message with preview option."""
        file_info = message_data.get('file_info', {})
        file_name = file_info.get('name', 'Unknown file')
        file_size = file_info.get('size', 0)
        file_path = file_info.get('path', '')

        # Create file message bubble with preview button
        message_frame = tk.Frame(self.messages_scrollable_frame, bg=self.colors['background'])
        message_frame.pack(fill=tk.X, padx=10, pady=5)

        # File bubble
        bubble_color = self.colors['message_sent'] if message_data.get('sender_id') == self.user_info['id'] else self.colors['message_received']
        bubble_frame = tk.Frame(message_frame, bg=bubble_color, relief=tk.RAISED, bd=1)

        if message_data.get('sender_id') == self.user_info['id']:
            bubble_frame.pack(side=tk.RIGHT, padx=(50, 0), pady=2)
        else:
            bubble_frame.pack(side=tk.LEFT, padx=(0, 50), pady=2)

        # File icon and info
        file_frame = tk.Frame(bubble_frame, bg=bubble_color)
        file_frame.pack(padx=15, pady=10)

        # File icon
        file_ext = os.path.splitext(file_name)[1].lower()
        file_icon = self.get_file_icon(file_ext)

        icon_label = tk.Label(file_frame, text=file_icon, bg=bubble_color,
                             font=('Arial', 24))
        icon_label.pack()

        # File name
        name_label = tk.Label(file_frame, text=file_name, bg=bubble_color,
                             fg=self.colors['text'], font=('Arial', 10, 'bold'),
                             wraplength=200)
        name_label.pack()

        # File size
        size_str = self.format_file_size(file_size)
        size_label = tk.Label(file_frame, text=size_str, bg=bubble_color,
                             fg=self.colors['text_secondary'], font=('Arial', 8))
        size_label.pack()

        # Preview button
        if file_path and os.path.exists(file_path):
            preview_btn = tk.Button(file_frame, text="👁️ Preview", bg=self.colors['accent'],
                                   fg='white', relief=tk.FLAT, font=('Arial', 9),
                                   command=lambda: self.show_file_preview(file_path, file_name, file_size))
            preview_btn.pack(pady=(5, 0))

        # Timestamp
        timestamp = datetime.fromisoformat(message_data['created_at']) if 'created_at' in message_data else datetime.now()
        time_str = format_timestamp(timestamp)

        time_label = tk.Label(bubble_frame, text=time_str, bg=bubble_color,
                             fg=self.colors['text_secondary'], font=('Arial', 8))
        time_label.pack(padx=10, pady=(0, 5), anchor=tk.E if message_data.get('sender_id') == self.user_info['id'] else tk.W)

    def get_file_icon(self, file_ext: str) -> str:
        """Get emoji icon for file type."""
        image_types = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        video_types = {'.mp4', '.avi', '.mov', '.mkv', '.wmv'}
        audio_types = {'.mp3', '.wav', '.ogg', '.m4a', '.flac'}

        if file_ext in image_types:
            return "🖼️"
        elif file_ext in video_types:
            return "🎥"
        elif file_ext in audio_types:
            return "🎵"
        elif file_ext == '.pdf':
            return "📕"
        elif file_ext in {'.doc', '.docx'}:
            return "📘"
        elif file_ext == '.txt':
            return "📄"
        elif file_ext in {'.zip', '.rar', '.7z'}:
            return "📦"
        else:
            return "📎"

    def format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        size = float(size_bytes)

        while size >= 1024 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1

        return f"{size:.1f} {size_names[i]}"

    def cleanup(self):
        """Clean up resources."""
        if self.ws_client:
            self.ws_client.disconnect()
        print("✅ Chat window cleaned up")
