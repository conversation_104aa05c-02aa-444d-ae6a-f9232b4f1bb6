#!/usr/bin/env python3
"""
Complete startup script for the Advanced WhatsApp-like Chat Application.
This script handles backend startup, dependency checking, and client launching.
"""

import os
import sys
import subprocess
import time
import requests
import threading
from pathlib import Path

def print_header():
    """Print application header."""
    print("=" * 70)
    print("🎉 ADVANCED WHATSAPP-LIKE CHAT APPLICATION")
    print("=" * 70)
    print("🚀 Features: Voice/Video Calls, File Sharing, Status Updates")
    print("💬 Real-time messaging with all WhatsApp-like features")
    print("🎯 Production-ready quality with advanced functionality")
    print("=" * 70)

def check_backend():
    """Check if backend is running."""
    try:
        response = requests.get("http://localhost:8001/health", timeout=3)
        return response.status_code == 200
    except:
        return False

def check_backend_dependencies():
    """Check backend dependencies."""
    print("🔍 Checking backend dependencies...")
    
    backend_packages = [
        'fastapi',
        'uvicorn', 
        'python-multipart',
        'python-jose',
        'passlib',
        'bcrypt',
        'websockets'
    ]
    
    missing = []
    for package in backend_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    return missing

def check_client_dependencies():
    """Check client dependencies."""
    print("🔍 Checking client dependencies...")
    
    client_packages = [
        ('tkinter', 'tkinter'),
        ('PIL', 'Pillow'),
        ('pygame', 'pygame'),
        ('cv2', 'opencv-python'),
        ('pyaudio', 'pyaudio'),
        ('plyer', 'plyer'),
        ('requests', 'requests')
    ]
    
    missing = []
    for import_name, package_name in client_packages:
        try:
            if import_name == 'tkinter':
                import tkinter
            elif import_name == 'PIL':
                from PIL import Image, ImageTk
            elif import_name == 'pygame':
                import pygame
            elif import_name == 'cv2':
                import cv2
            elif import_name == 'pyaudio':
                import pyaudio
            elif import_name == 'plyer':
                from plyer import notification
            elif import_name == 'requests':
                import requests
            
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name}")
            missing.append(package_name)
    
    return missing

def install_dependencies(missing_backend, missing_client):
    """Install missing dependencies."""
    all_missing = missing_backend + missing_client
    
    if not all_missing:
        return True
    
    print(f"\n⚠️ Missing packages: {', '.join(all_missing)}")
    
    choice = input("\nInstall missing packages automatically? (y/n): ").strip().lower()
    
    if choice == 'y':
        print("📦 Installing packages...")
        try:
            for package in all_missing:
                print(f"Installing {package}...")
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                             check=True, capture_output=True)
                print(f"✅ {package} installed")
            
            print("✅ All packages installed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install packages: {e}")
            return False
    else:
        print("Please install the packages manually:")
        for package in all_missing:
            print(f"  pip install {package}")
        return False

def start_backend():
    """Start the backend server."""
    print("🚀 Starting backend server...")
    
    script_dir = Path(__file__).parent
    backend_dir = script_dir / "backend"
    
    if not backend_dir.exists():
        print(f"❌ Backend directory not found: {backend_dir}")
        return None
    
    try:
        cmd = [
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8001"
        ]
        
        print(f"📂 Starting from: {backend_dir}")
        
        # Start backend in background
        if os.name == 'nt':  # Windows
            process = subprocess.Popen(cmd, cwd=backend_dir, 
                                     creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:  # Unix/Linux/Mac
            process = subprocess.Popen(cmd, cwd=backend_dir)
        
        # Wait for backend to start
        print("⏳ Waiting for backend to start...")
        for i in range(30):
            if check_backend():
                print("✅ Backend started successfully!")
                print("📍 Server running at: http://localhost:8001")
                print("📖 API docs at: http://localhost:8001/docs")
                return process
            time.sleep(1)
            print(f"   Waiting... ({i+1}/30)")
        
        print("❌ Backend failed to start within 30 seconds")
        return None
        
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return None

def setup_test_data():
    """Set up test users and data."""
    print("👥 Setting up test users...")
    
    test_users = [
        {"username": "alice", "email": "<EMAIL>", "password": "testpass123", "full_name": "Alice Johnson"},
        {"username": "bob", "email": "<EMAIL>", "password": "testpass123", "full_name": "Bob Smith"},
        {"username": "charlie", "email": "<EMAIL>", "password": "testpass123", "full_name": "Charlie Brown"}
    ]
    
    for user_data in test_users:
        try:
            response = requests.post("http://localhost:8001/auth/register", json=user_data)
            if response.status_code == 200:
                print(f"✅ Created user: {user_data['username']}")
            else:
                print(f"⚠️ User {user_data['username']} might already exist")
        except Exception as e:
            print(f"❌ Error creating user {user_data['username']}: {e}")
    
    # Set up general chat
    try:
        script_dir = Path(__file__).parent
        backend_dir = script_dir / "backend"
        setup_script = backend_dir / "setup_general_chat.py"
        
        if setup_script.exists():
            result = subprocess.run([sys.executable, "setup_general_chat.py"], 
                                  cwd=backend_dir, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print("✅ General chat room setup completed")
            else:
                print("⚠️ General chat setup had issues")
    except Exception as e:
        print(f"❌ Error setting up general chat: {e}")

def start_client():
    """Start the chat client."""
    print("🖥️ Starting advanced chat client...")
    
    script_dir = Path(__file__).parent
    client_dir = script_dir / "client"
    
    if not client_dir.exists():
        print(f"❌ Client directory not found: {client_dir}")
        return False
    
    try:
        cmd = [sys.executable, "main.py"]
        subprocess.run(cmd, cwd=client_dir)
        return True
    except KeyboardInterrupt:
        print("\n👋 Chat client closed")
        return True
    except Exception as e:
        print(f"❌ Error starting client: {e}")
        return False

def show_usage_instructions():
    """Show usage instructions."""
    print("\n" + "=" * 70)
    print("📱 HOW TO USE THE ADVANCED CHAT APPLICATION")
    print("=" * 70)
    print("\n🔑 LOGIN CREDENTIALS:")
    print("   • alice / testpass123")
    print("   • bob / testpass123") 
    print("   • charlie / testpass123")
    
    print("\n🎯 FEATURES TO TEST:")
    print("   ✅ Real-time messaging")
    print("   ✅ Voice & video calling")
    print("   ✅ Voice messages")
    print("   ✅ File sharing (photos, documents, audio)")
    print("   ✅ Status updates (24-hour stories)")
    print("   ✅ Emoji picker and reactions")
    print("   ✅ Group creation and management")
    print("   ✅ Contact management")
    print("   ✅ Search functionality")
    print("   ✅ Dark/light theme toggle")
    
    print("\n🌐 MULTI-USER TESTING:")
    print("   • Open multiple client instances")
    print("   • Login with different users")
    print("   • Test real-time communication")
    print("   • Try voice/video calls between users")
    
    print("\n" + "=" * 70)

def main():
    """Main function."""
    print_header()
    
    # Check dependencies
    missing_backend = check_backend_dependencies()
    missing_client = check_client_dependencies()
    
    if missing_backend or missing_client:
        if not install_dependencies(missing_backend, missing_client):
            print("\n❌ Cannot proceed without required dependencies")
            return
    
    print("\n✅ All dependencies available")
    
    # Check if backend is already running
    if check_backend():
        print("✅ Backend is already running")
    else:
        # Start backend
        backend_process = start_backend()
        if not backend_process:
            print("❌ Failed to start backend")
            return
    
    # Setup test data
    setup_test_data()
    
    # Show instructions
    show_usage_instructions()
    
    # Ask to start client
    choice = input("\nStart the advanced chat client? (y/n): ").strip().lower()
    
    if choice == 'y':
        start_client()
    else:
        print("\n👋 Setup complete!")
        print("Start the client manually with: python start_client.py")
        print("Or: cd client && python main.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Startup interrupted")
    except Exception as e:
        print(f"\n❌ Startup error: {e}")
        import traceback
        traceback.print_exc()
