#!/usr/bin/env python3
"""
Test script to verify the full chat application functionality.
"""

import requests
import json
import time

def test_chat_functionality():
    """Test the complete chat functionality."""
    base_url = "http://127.0.0.1:8003"
    
    print("🧪 Testing Full Chat Application Functionality")
    print("=" * 50)
    
    # Test 1: Health check
    print("1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Backend is healthy")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test 2: User registration
    print("\n2. Testing user registration...")
    test_user = {
        "username": f"testuser_{int(time.time())}",
        "email": f"test_{int(time.time())}@example.com",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{base_url}/auth/register", json=test_user)
        if response.status_code == 200:
            print("✅ User registration successful")
            user_data = response.json()
        else:
            print(f"❌ User registration failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ User registration error: {e}")
        return False
    
    # Test 3: User login
    print("\n3. Testing user login...")
    login_data = {
        "username": test_user["username"],
        "password": test_user["password"]
    }
    
    try:
        response = requests.post(f"{base_url}/auth/login", json=login_data)
        if response.status_code == 200:
            print("✅ User login successful")
            token_data = response.json()
            access_token = token_data["access_token"]
            headers = {"Authorization": f"Bearer {access_token}"}
        else:
            print(f"❌ User login failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ User login error: {e}")
        return False
    
    # Test 4: Get chat rooms
    print("\n4. Testing chat rooms retrieval...")
    try:
        response = requests.get(f"{base_url}/chat-rooms", headers=headers)
        if response.status_code == 200:
            rooms = response.json()
            print(f"✅ Retrieved {len(rooms)} chat rooms")
            if rooms:
                test_room = rooms[0]
                print(f"   First room: {test_room['name']} (ID: {test_room['id']})")
            else:
                print("   No rooms found, will use General Chat")
                test_room = {"id": 4, "name": "General Chat"}  # Default room
        else:
            print(f"❌ Chat rooms retrieval failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat rooms retrieval error: {e}")
        return False
    
    # Test 5: Send a message
    print("\n5. Testing message sending...")
    message_data = {
        "content": f"Test message from {test_user['username']} at {time.strftime('%H:%M:%S')}",
        "message_type": "text"
    }
    
    try:
        response = requests.post(f"{base_url}/chat-rooms/{test_room['id']}/messages", 
                               json=message_data, headers=headers)
        if response.status_code == 200:
            print("✅ Message sent successfully")
            message_response = response.json()
            print(f"   Message ID: {message_response['id']}")
        else:
            print(f"❌ Message sending failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Message sending error: {e}")
        return False
    
    # Test 6: Get messages
    print("\n6. Testing message retrieval...")
    try:
        response = requests.get(f"{base_url}/chat-rooms/{test_room['id']}/messages", 
                               headers=headers)
        if response.status_code == 200:
            messages = response.json()
            print(f"✅ Retrieved {len(messages)} messages")
            if messages:
                latest_message = messages[-1]
                print(f"   Latest message: {latest_message['content'][:50]}...")
        else:
            print(f"❌ Message retrieval failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Message retrieval error: {e}")
        return False
    
    # Test 7: Get users
    print("\n7. Testing users retrieval...")
    try:
        response = requests.get(f"{base_url}/users", headers=headers)
        if response.status_code == 200:
            users = response.json()
            print(f"✅ Retrieved {len(users)} users")
        else:
            print(f"❌ Users retrieval failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Users retrieval error: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 ALL TESTS PASSED! Chat application is fully functional!")
    print("\n📱 You can now run the client with: python client/main.py")
    print("🔑 Login with any existing user or create a new account")
    print("💬 Start chatting in the General Chat room or create private chats")
    
    return True

if __name__ == "__main__":
    success = test_chat_functionality()
    if not success:
        print("\n❌ Some tests failed. Please check the backend logs.")
        exit(1)
    else:
        print("\n✅ All functionality tests passed!")
        exit(0)
