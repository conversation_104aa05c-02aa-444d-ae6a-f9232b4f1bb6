#!/usr/bin/env python3
"""
Simple script to start the backend server from the correct directory.
"""

import os
import sys
import subprocess
from pathlib import Path

def start_backend():
    """Start the backend server."""
    print("🚀 Starting Advanced Chat Backend Server...")
    
    # Get the directory where this script is located
    script_dir = Path(__file__).parent
    backend_dir = script_dir / "backend"
    
    # Check if backend directory exists
    if not backend_dir.exists():
        print(f"❌ Backend directory not found: {backend_dir}")
        print("Make sure you're running this script from the Chat Application directory")
        return False
    
    # Check if app/main.py exists
    main_py = backend_dir / "app" / "main.py"
    if not main_py.exists():
        print(f"❌ Backend main.py not found: {main_py}")
        return False
    
    print(f"📁 Backend directory: {backend_dir}")
    print(f"📄 Main file: {main_py}")
    
    try:
        # Change to backend directory and start uvicorn
        print("🔄 Starting uvicorn server...")
        
        # Use subprocess to run uvicorn from the backend directory
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8001",
            "--reload"  # Enable auto-reload for development
        ]
        
        print(f"🔧 Command: {' '.join(cmd)}")
        print(f"📂 Working directory: {backend_dir}")
        print("=" * 50)
        
        # Start the server
        process = subprocess.run(cmd, cwd=backend_dir)
        
        return process.returncode == 0
        
    except KeyboardInterrupt:
        print("\n👋 Backend server stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error starting backend: {e}")
        return False

def check_dependencies():
    """Check if required packages are installed."""
    print("🔍 Checking backend dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'python-multipart',
        'python-jose',
        'passlib',
        'bcrypt',
        'websockets'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Install with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All backend dependencies available")
    return True

def main():
    """Main function."""
    print("💬 Advanced Chat Backend Starter")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies first")
        return
    
    # Start backend
    print("\n🚀 Starting backend server...")
    print("📍 Server will be available at: http://localhost:8001")
    print("📖 API docs will be at: http://localhost:8001/docs")
    print("🔄 Press Ctrl+C to stop the server")
    print("=" * 50)
    
    success = start_backend()
    
    if success:
        print("✅ Backend server started successfully")
    else:
        print("❌ Failed to start backend server")

if __name__ == "__main__":
    main()
